{"_id": "dotenv", "_rev": "658-d5eed7dadd4b87c05cdea47d51c80ea2", "name": "dotenv", "dist-tags": {"next": "16.1.0-rc2", "latest": "17.2.1"}, "versions": {"0.0.1": {"name": "dotenv", "version": "0.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.0.1", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "2b95e8ab1fea2b7b76a26fc90441a2a8b637a168", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.0.1.tgz", "integrity": "sha512-nXiy+U3L+oxybQRiv87sTWPNdk8XS4Q743kLpWV9gzpsM1lr15in2ryBlpT8pgKwJRSroj4AY2/BFzhXYrFeLA==", "signatures": [{"sig": "MEYCIQDaT7DVstEBSl99sa2rdnKv+EvXWfHS/ZHHiplmdYs3ZwIhAJa67gj3SzcNEGwX2OrTuowvXa/938PUaRvj0vT9Z82j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.2.21", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.0.2": {"name": "dotenv", "version": "0.0.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.0.2", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "e635546459df85c4ab71386a8dd3a2087c436fc7", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.0.2.tgz", "integrity": "sha512-TTJxj8jTrJkWNAeBcIbfe0iBjjli+Lp/9wk4uadL9bqkO2mEhsiezseK1MSNmwhB+7Wy7eol8ddIqEjq6/7jzA==", "signatures": [{"sig": "MEYCIQCrQaDMK2DU7uULswNXXIqiawiVbTK0QwTrqkiZ6+9WWAIhAKl7dYgKRgcpxzjtpH5qh3LefyatFxASjG8zHiB9S2to", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.0.3": {"name": "dotenv", "version": "0.0.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.0.3", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "bc2b287757ce957592212974525f571079f5850c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.0.3.tgz", "integrity": "sha512-cbrDt6063aknvuh5rfIQHobH/cmMX1CQZiyHE6pFnzJxWJj7YWWIeNND3o9AHnHwcMo4i18NOmDKoW2YpsDHHg==", "signatures": [{"sig": "MEQCIELaX0+zjZx5hs0z1rRCPyiMEaecnfeASXN7GQtISW6BAiA/NHVbZf7DKWYVpWqm9Koxm3ackTw19EoDY/BwMofCOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.0.4": {"name": "dotenv", "version": "0.0.4", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.0.4", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "5a056dfcd79d02992c74f8770a5da352a85c2a6d", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.0.4.tgz", "integrity": "sha512-dDNhvtM466OIP3Dy6D1A8hWgSkNWuFX4z57Rof+Bbn6nA7TyyDTTWKSGSRPO9imXareBVc0+3ihWmhJOuLd83A==", "signatures": [{"sig": "MEYCIQCDeOxn3lnF9/SErHrq4EM5PpV6ueeGOWgC1SC6WI3gTQIhAIAhIyQLiwoTncbvAKIpHcxQ5y+f6mbosaEvVhYT8ItM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.0.5": {"name": "dotenv", "version": "0.0.5", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.0.5", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "2a4efc3b2009a7b23821d099d325803bb291f046", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.0.5.tgz", "integrity": "sha512-bJpNIHULYDIsSL4ZWZc9rPEqf8HVECM7D6xnMM/H3kjRDlW2j5D+k7DlTWjJ/USJ3XMKkLyXS69wu27gQF2w7w==", "signatures": [{"sig": "MEQCIArkKc49grXKcXnmWkICHSok5AZSsOrwA4luWtQSGA5WAiBPSC1KFuprLWflhW+NyRkMqi9QwJ8c+cciZx5dFcq0Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.1.0": {"name": "dotenv", "version": "0.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.1.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "e880aa04831a2aec04c70a741a84d07f6443d2ff", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.1.0.tgz", "integrity": "sha512-gbDFN/Pva5zqeU/w7J2UmlUbP/wRuwttl6/waQ0yDjSsA1+ezPce5OTUGkaiOpvYsgNk2OnRs+Y5YR8Cc/p7qA==", "signatures": [{"sig": "MEYCIQDd6lhf8WusK97ylgGT1HRm+Fc8zNzobfi7OpAp8p107QIhAIDWLoHXjWh1CtnoBbkh9CtMbI45W/Sezj+D3o/c7aQN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.1.1": {"name": "dotenv", "version": "0.1.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.1.1", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "03c6a24710396fd017b98eccb1ed285d58a6e3a1", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.1.1.tgz", "integrity": "sha512-Cfu8pCJ050OwW0UL33yFchES5U1OAy8L5jJ6j7MzoWZGUtz+Q+dNTmF/1vNQKtCrOt0EyEWx/mwWeIWhDajgvw==", "signatures": [{"sig": "MEQCIEu4wVd8tuBbdWZvV6gFSiuWvOztD4OeAcDN9tzM+fhZAiBBGfloluX4DhiUWTKiIFXkTBipEVFeMvfFt5RLwCCf3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.1.2": {"name": "dotenv", "version": "0.1.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.1.2", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "7a09accf29e3033ac9ab845259225d46fe509c6a", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.1.2.tgz", "integrity": "sha512-s0oLtztYIPGktgIIcZn2sXnor+0IHlpnw2kVNNnYzI+YwLGD9ponlE8hW2EwCozAZLSuWugf6txcBJFzqw+jQQ==", "signatures": [{"sig": "MEQCIHJVj0b2W+o0sp62OS0qhKJPlVDhsX8P5g7NQvqzooNdAiAMhJMH9DOri+BPUrHHLdn5PmNwxaHm5ebl7ftxRX6HTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.0": {"name": "dotenv", "version": "0.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "369043b6823c02c35ea3a1c631dca1837222d68e", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.0.tgz", "integrity": "sha512-oV65sjqqlbg23/TAfploI7rV0GF6vuDqNpXqq3rOYFaaIO9JjWn/3O1R4XVD7hOpYl+FGAF88qx3X5tQ95mhfg==", "signatures": [{"sig": "MEQCIBr3RciJ2i2VObwbic47cF3BPR4BPGq3YqIJpnSsDaYYAiBnPJLnh1xNjcl+QnsY43CoDsFOMpeMhEULv652KmwFDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.1": {"name": "dotenv", "version": "0.2.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.1", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "00843ca2b7eddce82b788e4b8ad720f202348de3", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.1.tgz", "integrity": "sha512-7gnyKgNgkBqrQG4OOIP5uKDGDA0xTxuu8ht4neyDcnO30rYwDxqkavVGf3zVv80jC+wGX/rqMKRTxJqxt/XcqQ==", "signatures": [{"sig": "MEYCIQD2Ec7uVT/mPYEKaIA6F7FRAHE/luy61r9qI38b9GDnNQIhAKyShRZqJuivIKuNi7Yj4QH9sskgPmnVtsAE+Y6yyi0m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.2": {"name": "dotenv", "version": "0.2.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.2", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "79341a91537909677fcd9fa234b419c2f20babc7", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.2.tgz", "integrity": "sha512-IndRSPowQcbOBxwRR7XRURxnov//ix1NupLx3mVGX43kpPDquMQcUYlaxDDhY0/17b/m+KqojMTYEzNKxfm/oA==", "signatures": [{"sig": "MEUCIBUwxy/nY2/PY0Q6vs1RKWVlPPqXkRmudoVq40NPXZVzAiEA1fyHsCvKa/JZf+zkSlhQbTsF14S/KSzH0g6zvEy5/go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.3": {"name": "dotenv", "version": "0.2.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.3", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "e3bbd5925dacc682add8c9e27928b3d1fac190d8", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.3.tgz", "integrity": "sha512-HTo15mgprFB/VnhKRton6SU+0maNMuICwWB9gVLI24e7YZ9BUy1xIdXsjpd4RqzJkCQiNtoL5xi04EmQ1fpvhQ==", "signatures": [{"sig": "MEUCIQD7XRM4x4S9Qmh7ezhAkkywBuFhQYyyLuxeo2OBedI1xAIgODrg3qHuWVYnVTqiouky/+wboQsQhKvImI1Gq7xIAKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.4": {"name": "dotenv", "version": "0.2.4", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.4", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "83aaf52b7375ad4519b39b56c3af79df7355b38a", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.4.tgz", "integrity": "sha512-p6QMx0WyKHr5ESpsXVzDS+tGFORnbPD5IG+vzOxOGC+786wxv/3XxznGFe7ParN26y22Y4h4o6gTN2olk62Dew==", "signatures": [{"sig": "MEUCIGcieOXLgHZhvsJwzXSTafpjKkDPTSSGtwG1oWgHo6rPAiEAmoiSOhP8pGOv65a4RX6J55KkoacU3hE0O0pmVlblEYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.5": {"name": "dotenv", "version": "0.2.5", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.5", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "666c0e82779055cbe146cf1898548249a09c8b2c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.5.tgz", "integrity": "sha512-tPWm1EiADUii/BlJJmNnSqQwddhF63R5zUtg+6ophz1EQIcsH/2j8+oz+VMQYHS6uQ5zifF/fCky6n1FhkCkSQ==", "signatures": [{"sig": "MEUCIQDviSBV4fzJZfkOhd/7Rp/t6xgfD0pkm36CNJC75OiM8wIgBjiewFIa2mrUfcMkp0cY6MnGWc7XncCtnxEerUjg4vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.6": {"name": "dotenv", "version": "0.2.6", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.6", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob2dot0", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "4dc9434e83478fa19fd9728a6e7b73b4e8e8c131", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.6.tgz", "integrity": "sha512-IiKjOzMZzsnX7zLNRtBZZyqQUAbmffT1A07Fn3JCKBPwN0N1YWKeDmFWhU2XkGWOHFHcuodSiA0XnUHVH5N/Nw==", "signatures": [{"sig": "MEUCIC8AYk+UvBs+oSIO0+8LHVRBtJ4//Wd67bC9cyDF5wwRAiEAgtHC11rGtXPVci3bfu4sDOmInOrhZcckDvjbO0l+pV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha test/*.js"}, "_npmUser": {"name": "jacob2dot0", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.7": {"name": "dotenv", "version": "0.2.7", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.7", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob2dot0", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "7f3525d2193aa8892aabb0e407bd024f7c0e12da", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.7.tgz", "integrity": "sha512-dX/w/UYcf5C2yfr3k6FwYtrHjIpZN7QS+b79juKKcgCGzj2rQdqreykY9ATU0WHbQuviP9uvIxdxvEFiJx2bCw==", "signatures": [{"sig": "MEUCIAsORwa0yJ8+c3fmAp5L8UxIeFpMURsiw9Zx+giDOwOeAiEA9nkb2PHbCBQQOw0mX45ru5ZSIJFkF+fR8U6zcu0i+14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.2.8": {"name": "dotenv", "version": "0.2.8", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.2.8", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob2dot0", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "22795b2d6d6bbfa2129ed7b3ab8338f09bae94f9", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.2.8.tgz", "integrity": "sha512-bLB2jikmV3t2r/E1+lcxE/L6WhshXP9IQ/rrZySUMbRVWkyr536dWttZVbGuGjNQZwFefiYlCIoHlnblwmz8bQ==", "signatures": [{"sig": "MEUCIQCRP2x4dlgZiZFOhwCpqrvGpandaO4vK5EHf+T4AgGsoAIgF8wErsKFnP90L5mxdfIMe1Snx7nddi4P4MgtAxBfyME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.3.0": {"name": "dotenv", "version": "0.3.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.3.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob2dot0", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "e15c801de875b3377d8957e28ada95040faaccfd", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.3.0.tgz", "integrity": "sha512-ek7iZGT2zNxkfr8c6Hu0CgxM1a9DSG3g3PYRkaHp7TgUUmFsjHAzvPi1HjbGfnOimNs9nZH5/KEPbI9bRqoBIw==", "signatures": [{"sig": "MEYCIQCJqeSq50zwWPDQtDXx2loK55zRyKtlzuAaoorbflPVagIhAPlcMy7evbHjXn8jM9V/VeAtoDfRZVJkeBkp1zAO5oVQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.4.0": {"name": "dotenv", "version": "0.4.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.4.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob2dot0", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "f6fb351363c2d92207245c737802c9ab5ae1495a", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.4.0.tgz", "integrity": "sha512-F5/eIZtAq1L+A+7KTG85HQnSSrQYVQ49CM/3EzoYgpwjSW0GZomI/kRuEZnNB0HZvTyXP+1KrhNTJL2+fnuKFw==", "signatures": [{"sig": "MEUCIQDp5mdUDrnVjjPAOKQTr5Q8aWDQI837TtHHuy+z/UjolAIgC0RHkVirkVS35r3jq2EWSrhJNEFOoXhMskZrA8YIbIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "devDependencies": {"mocha": "", "should": ""}}, "0.5.0": {"name": "dotenv", "version": "0.5.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.5.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "1631b589ad12acb3bbcc44dfbc3bd8e40ae204e5", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.5.0.tgz", "integrity": "sha512-45cwv5jaNrY/RXHGiOgTnn0VqzDsDOefCciOphkLtBQeOoFjoyVXQiLwUA6lSjLuUNH0lRXQB7N/izMbMKH99Q==", "signatures": [{"sig": "MEUCIDIo9c1Tr+ceTPGBld6CF2khwTU/n9vYwfN6GM/Gj8C8AiEA5jirA8L3RYVYxNjRs4cNJRu5mbkOT8FlKi+HJ2w1YBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "1631b589ad12acb3bbcc44dfbc3bd8e40ae204e5", "gitHead": "fe0732061c9c2c8537d7783c64099dd2dc649bb0", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "jcblw", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "_nodeVersion": "0.10.35", "devDependencies": {"mocha": "", "should": ""}}, "0.5.1": {"name": "dotenv", "version": "0.5.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@0.5.1", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/scottmotte/dotenv", "bugs": {"url": "https://github.com/scottmotte/dotenv/issues"}, "dist": {"shasum": "87d181f76102afd1a2b85c277ddd8b8795dc565b", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-0.5.1.tgz", "integrity": "sha512-ON/UM3hCHe9lSUa54BDbpzQtaEX0BCNMAX2+oW+Sw5wFDLfqwqcQ1aFQtnd0YLhgiLrpH3mrIBeR1WbgcoCLFw==", "signatures": [{"sig": "MEYCIQDAmcCR9469XcE8jiGVrnR90pNfyHBgLBss1D+30+xL6QIhAO/SHT53p077CO0v9WbNYyRWP5qnThVCVI1SUe3WDZ2Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "87d181f76102afd1a2b85c277ddd8b8795dc565b", "gitHead": "09c82858d0319f00aa9ce02a8fe13d19a7c51f6f", "scripts": {"test": "mocha test/*.js"}, "_npmUser": {"name": "jcblw", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/scottmotte/dotenv.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Loads environment variables from .env", "directories": {"test": "test"}, "_nodeVersion": "0.10.35", "devDependencies": {"mocha": "", "should": ""}}, "1.0.0": {"name": "dotenv", "version": "1.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@1.0.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "fdc527fc66411c61d74a3ab9d199ebf874532cd4", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-1.0.0.tgz", "integrity": "sha512-Ic4s6jdG8RIhnqpHSDgQl62Bkldl/hihAnYZj+B8jce58KpiegYDoqafjwiRijqW7ff2AtVTQIb6pqG/Enj8fw==", "signatures": [{"sig": "MEYCIQCa4PT4KVbiz07JHO+GOYWPX7LK0YtigitovkXOGxudBwIhAINSzwBP5UQYZTEa1LgzJ5JV148eviu+ekhCmVKxH7wA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "fdc527fc66411c61d74a3ab9d199ebf874532cd4", "gitHead": "a75af055a75b61da764d7022cbb3f1c82552f2b1", "scripts": {"test": "lab test/* --coverage && standard"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {}, "devDependencies": {"lab": "^5.3.0", "sinon": "1.12.2", "should": "4.4.2", "standard": "^2.10.0"}}, "1.1.0": {"name": "dotenv", "version": "1.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD", "_id": "dotenv@1.1.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "97438b62f487ff6c9b157b0f9e713b335e590b4e", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-1.1.0.tgz", "integrity": "sha512-XHSO+oL0Yj564+oITJyEkjLTDlMPS9awzV2j/+FaInnxnZ8OrGnx18qY/ltl5ql7t7kJH6hScxPwIRf6PUPH+A==", "signatures": [{"sig": "MEQCIGUXpqP7kmM9bn9QeD2peeX19koWTOpBTywIeYd7YW5BAiAIBULDnnM+R0/STDBARIoMWY1CZ0/7h5NJMl5r/ke57w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "97438b62f487ff6c9b157b0f9e713b335e590b4e", "gitHead": "d2c81b6b41c5b283afdaf20456eb59e3e3a9f3a8", "scripts": {"test": "lab test/* --coverage && standard"}, "_npmUser": {"name": "jcblw", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "1.2.0", "dependencies": {}, "devDependencies": {"lab": "^5.3.0", "sinon": "1.12.2", "should": "4.4.2", "standard": "^2.10.0"}}, "1.2.0": {"name": "dotenv", "version": "1.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@1.2.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "7cd73e16e07f057c8072147a5bc3a8677f0ab5c6", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-1.2.0.tgz", "integrity": "sha512-UHFQewZEALYCDzQa+xqjiMA7uRKCWWwd+HjxyD+101MMfMaRXJncTfH6k/SvNrV7479rf8F9lYiCwkMaSkGy0Q==", "signatures": [{"sig": "MEUCIDL6H7pQABYNRdaAc4bVZU+TLOZLEMgIX3QH8JhGmAPWAiEAnJPmbNSZTfrPxX3XHE3Sjg20/yjt598/9GsSfvLwpSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "7cd73e16e07f057c8072147a5bc3a8677f0ab5c6", "gitHead": "940d101b4df66157cf752b611fd2292060878b44", "scripts": {"lint": "standard", "test": "lab test/* --coverage && standard"}, "_npmUser": {"name": "jcblw", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "1.2.0", "dependencies": {}, "devDependencies": {"lab": "^5.3.0", "sinon": "1.12.2", "semver": "^4.3.6", "should": "4.4.2", "standard": "^2.10.0"}}, "2.0.0": {"name": "dotenv", "version": "2.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@2.0.0", "maintainers": [{"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "bd759c357aaa70365e01c96b7b0bec08a6e0d949", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-2.0.0.tgz", "integrity": "sha512-Y+zZAmv7p2zOdpyZcSIA+aIxohsyfTcNaMeh3YJn9exq85bQhso65Wz9IhjYYNB4zyvXnfi7Ae+FuygARljVJw==", "signatures": [{"sig": "MEUCIQCT+iOGvtgVnGSTrk7HSwBsnke8Re0jCL/a8wQJG31WPwIgaKjxWRL84mMUNCLRJLzoqCgMqUPXPoqHCmBTnUCR0/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "bd759c357aaa70365e01c96b7b0bec08a6e0d949", "gitHead": "97d1a99d96b8992e54480b81880a6187de81c86b", "scripts": {"lint": "standard", "test": "lab test/* --coverage", "posttest": "npm run lint"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {}, "devDependencies": {"lab": "5.17.0", "babel": "5.8.23", "sinon": "1.16.1", "semver": "5.0.3", "should": "7.1.0", "standard": "5.3.0"}}, "3.0.0": {"name": "dotenv", "version": "3.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@3.0.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a007eb14a16444adbfafac936f33299bc08f9ba7", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-3.0.0.tgz", "integrity": "sha512-u+ZrOYjzGbYKDWWkpjR41o3bUDvg7EarzKtsAmO9O3pWKXWdqfK8R5W1repRfam2JgY3cZyHtu3YZZJNGvUteQ==", "signatures": [{"sig": "MEQCIDiCRkPK7vKlOhOE/fngeGHeJ7kbbNoPXd5G55l0+NTOAiAHoffs8uMLyICMDCUHNtXkq53CzJkrB8pqHgSsI5k6cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "a007eb14a16444adbfafac936f33299bc08f9ba7", "engines": {"node": ">=4.6.0"}, "gitHead": "f7835437f6d55379e39a035eb8e8a64590152a78", "scripts": {"lint": "standard", "test": "lab test/* -r lcov | coveralls", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {}, "devDependencies": {"lab": "11.1.0", "babel": "5.8.23", "sinon": "1.17.6", "semver": "5.3.0", "should": "11.1.1", "standard": "8.4.0", "coveralls": "^2.11.9", "standard-markdown": "2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv-3.0.0.tgz_1483816102014_0.7347200366202742", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.0": {"name": "dotenv", "version": "4.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@4.0.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "864ef1379aced55ce6f95debecdce179f7a0cd1d", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-4.0.0.tgz", "integrity": "sha512-XcaMACOr3JMVcEv0Y/iUM2XaOsATRZ3U1In41/1jjK6vJZ2PZbQ1bzCG8uvaByfaBpl9gqc9QWJovpUGBXLLYQ==", "signatures": [{"sig": "MEUCIB0hDcnmqvEQ3dXaFSPYOH+9vnE/9OEJcmrj3Y1hC6gvAiEA6LS1gQpV42m3/Ivu+nNVZqwK0EJCkAIRnkbX59KBttI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "_from": ".", "_shasum": "864ef1379aced55ce6f95debecdce179f7a0cd1d", "engines": {"node": ">=4.6.0"}, "gitHead": "fdd0923e82e12a6e29b65898990201857141e75d", "scripts": {"lint": "standard", "test": "lab test/* -r lcov | coveralls", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {}, "devDependencies": {"lab": "11.1.0", "babel": "5.8.23", "sinon": "1.17.6", "semver": "5.3.0", "should": "11.1.1", "standard": "8.4.0", "coveralls": "^2.11.9", "standard-markdown": "2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv-4.0.0.tgz_1483816132917_0.4720889476593584", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.0": {"name": "dotenv", "version": "5.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@5.0.0", "maintainers": [{"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcblw", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0206eb5b336639bf377618a2a304ff00c6a1fddb", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-5.0.0.tgz", "integrity": "sha512-p4A7snaxI9Hnj3GDWhTpckHYcd9WwZDmGPcvJJV3CoRFq0Dvsp96eYgXBl9WbmbJfuxqiZ2WenNaeWSs675ghQ==", "signatures": [{"sig": "MEUCIQCp+gZAzfLrAtJ0wFEHEuMByo5gWXHHG+G6eVS6il24MAIgCe7WA9iqgcn3JJ50+e3MYv0dzoZE8DPNNPqgltFM2g0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/main.js", "engines": {"node": ">=4.6.0"}, "gitHead": "472db2e026a3b7ced5dbc6b2f2704a1e81ab1bca", "scripts": {"lint": "standard", "test": "lab test/* -r lcov | coveralls", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {}, "devDependencies": {"lab": "11.1.0", "babel": "5.8.23", "sinon": "1.17.6", "should": "11.1.1", "standard": "8.4.0", "coveralls": "^2.11.9", "standard-markdown": "2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv-5.0.0.tgz_1517267710049_0.7447055950760841", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "dotenv", "version": "5.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@5.0.1", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a5317459bd3d79ab88cff6e44057a6a3fbb1fcef", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-4As8uPrjfwb7VXC+WnLCbXK7y+Ueb2B3zgNCePYfhxS1PYeaO1YTeplffTEcbfLhvFNGLAz90VvJs9yomG7bow==", "signatures": [{"sig": "MEUCIQDNL1YrAgJS3tV0H6fxc98Ijlb9nPwtcro774AAeGNq3QIgTkX1e2O7QTPZQqkUFHYWp9YwAR0QnxL9y9uH1hc9yP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17449}, "main": "lib/main.js", "engines": {"node": ">=4.6.0"}, "gitHead": "ad8690735a20b53c44936bb0597e4a5654853426", "scripts": {"lint": "standard", "test": "lab", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md", "ci:coverage": "lab test/* -r lcov | coveralls"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"lab": "^14.3.2", "babel": "5.8.23", "sinon": "1.17.6", "should": "11.1.1", "standard": "8.4.0", "coveralls": "^2.11.9", "standard-markdown": "2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_5.0.1_1519677989735_0.813087417647604", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "dotenv", "version": "6.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@6.0.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "24e37c041741c5f4b25324958ebbc34bca965935", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-6.0.0.tgz", "fileCount": 13, "integrity": "sha512-FlWbnhgjtwD+uNLUGHbMykMOYQaTivdHEmYwAKFjn6GKe/CqY0fNae93ZHTd20snh9ZLr8mTzIL9m0APQ1pjQg==", "signatures": [{"sig": "MEUCICXp9Ms46rCbB97PJqiNsGV6+oI9zhebXU4qOLOrnyksAiEAvxcH12iel1sLn6mSa9/yFkOl1r76+p9P5h3G+ZRUhEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEt2sCRA9TVsSAnZWagAA6ksP/Rc42AeaEVbDIrEZVjmn\nj9vEw9kjNQXw0dIlU3xfKmyiah8BvJ5GJGNvKVkw31mit0vCq+ozhu0RaO9Q\nXSssno4n1WNdkI94diVmq2t1RYld+ygeo10aTNCS2ah/uAoD+OnfztsJs+qR\nZkCGUZfo4cTeGZ4y4jrhFlNJZFU1Dam+zk9o8ukluCa7l6cGXwbigZ4+SVdu\n1KkSGd4wSx6wjjznHAi6HUtBotQrz3hKhUXF65RaSmbey6en3nrJRYFxcbCS\nHts53QEJTQsC8FrDWj3wKf6o81R+U5JS/kFRIS7POPe3I4GU026VY/CP5DNA\nx4NTJ5molEBYTXxaFFXW3S3uXfxLnpAMdUWM7k420LzyUSFMurTA/aRDo6wW\nwpTn7VsWWlc3a7RtK3ERsBhB6ZH5iexQi9p5/V0KHP1bMwKhuHkw22Hy/cYt\nS4GgDigLaOwFYJ3j1pvyr6GEmtG+VkSRlqkveB3rcL54iZnsu3QYZKrD9Bm8\n2wE7M82DmwIBilOAIlg+RQhK4jSocwwYlEoq8WxIo0N9ctvskj12dxorZU6a\nRGmCBwc0PI/sfhdv2eGzhQgicnPTxCzC+HMPHUIpcmtR11wwUQQAS//xTqqB\nuDYPoDzrbN6bZSVWakPMvx8iLy5GpDkTjCAiUeFzJdEMmIC5f5otgMPkVhzL\n5wB3\r\n=wqj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=6"}, "gitHead": "740c2f3768d34e9f3f511d20285a6b9063b0cee4", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^11.1.4", "sinon": "^5.0.2", "standard": "^11.0.1", "standard-markdown": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_6.0.0_1527963051221_0.014471018158733528", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "dotenv", "version": "6.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@6.1.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "9853b6ca98292acb7dec67a95018fa40bccff42c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-6.1.0.tgz", "fileCount": 10, "integrity": "sha512-/veDn2ztgRlB7gKmE3i9f6CmDIyXAy6d5nBq+whO9SLX+Zs1sXEgFLPi+aSuWqUuusMfbi84fT8j34fs1HaYUw==", "signatures": [{"sig": "MEQCIEaWX2sz8euwWEOm0UB//HURfRsq8kGFGIP3xFe3jzuKAiAl+cNNfkg8fIgL13cDJ+sgXvq8Ch84KDfvi5kDcpqiPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu+82CRA9TVsSAnZWagAALqYP/1fFvat46ss8cJaOCm9B\n3nnpEckAzhuHGJts9+PKTE/R+Eol6OLfHHaTg6T/Sfd+U1pCd63AYAjzsGN9\nV8jMowGaCxuKHY1Eag7btNiTLbm59i8QKevv0QG2lYwO5nxw/oDYN86yUZY8\nwkCU8RrE/5lgiNgw7Fqk26GeBU7n5L0fGtjOpB6ez7wdNZsZCtFavIabjYRM\nJ5tkXWTazIXXjTwZGq4MBzr0GNGa11sEMyp6Tlv8uEqFb2z1FIC2FnIWvFml\nsepwjOeawcMaaTV0JKCAcX2zY1n/ZiUMDSJ9LtjX+aQsm8lt5F68Q6txw1Is\n3EAnGW+mX0UQ9J5YFhhpgpolBnzvww7tdRqM4wW71eIGLzYLwvD7gDVs/RdE\nTxP4JGPtXPU3k9AhDsrP5e0TWwTgrwaTaJRTsvfQdo+0J/K5qkrB2QsUd8cP\nRzoIMkUUxvt62xMhvjueX0i2eNS2HhoVOdkcCkanL6t2N34fgfLwnUIRL0/7\nn0WXEhfw+GK/JXfuuCrKTSiLYmYg2MTDbHDfxhnerKIPNpkztL+m4X12/0RM\nOCwakcueaJj5Ryc/n+noa8TrFLlWiAqmp3KMwSHJHyH8bahXVSTCUrGpJA1n\n6be4LKD6X/vkjHeEky+dBMn9M4afVEJmsz9PX3Ui7zr+V5c+z7Lb73qMhCB8\np5CD\r\n=t1DG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=6"}, "gitHead": "78546c19e6c1636ce4ad85e9529b0b9ec6e67acf", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "lint-md": "standard-markdown", "pretest": "npm run lint", "postlint": "npm run lint-md"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "sinon": "^6.3.5", "flow-bin": "^0.82.0", "standard": "^12.0.1", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_6.1.0_1539043126164_0.5036199602414251", "host": "s3://npm-registry-packages"}}, "6.2.0-rc1": {"name": "dotenv", "version": "6.2.0-rc1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@6.2.0-rc1", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "2c9b92e1fbad290eb382e031746a75dd0c242575", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-6.2.0-rc1.tgz", "fileCount": 10, "integrity": "sha512-ZGwgDZJPYssjmare9pCya7qEDuuUL+OVVP4yqYQWV8i4+De3g9xU5dYSSGykIf6GAxob1+zUKE2ItxWt+ciyfA==", "signatures": [{"sig": "MEQCIDyC5Ejj3zjgGDv/DG8bXT8D2sZF3rXBQTMdGfIMPIhDAiBmrOJYXvlQa4rK2U/RqDYo1DkHzOdSmDhvDhURxy8k7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb23GpCRA9TVsSAnZWagAA0QUP/3nCAzm8jB1IsjFFmNVR\nP5/wrBosKyhM8N3OxiLO/rpu1tnxxzeLEQrFYPxvxnnHtkgst1DGCnK7BCEJ\ng+FwQNfpL4guTzTJEDiAk/39sbS+y3UHvPwHhXBeIm/fNlBDa3tnXwGTSJbX\no/Ah5kX+55ZIAu/ni+iNzBtDu35JnvqLfzLkUVE1HTAzlAFXbuFbQK2eKQPI\nC2e1iRLQkuiQW3j9akMOENkiqIy7aBSOvWuzgm1YIcy9jk25N/UTLQVkaMhU\n7t5ukJcwI9ud7e1HsYwHnkDNC0AnoyLzlQB836qrzoB+fNLmM3ZGJJAC6nwI\nC4k7ZGCTEBrLhQyBSk9zxyOb5vgNSTWbVE+b6DB4MJgaiGkS61n7Dma/QvOn\nx7YCem2jrNd+lhNhN8cKllx0/z2kJp3pNWuhnRB7dq05vWM+a0lN1I90TFwJ\nLb1w6966uaFTUDEDEVD7GryBluIELV33nKVGONm03pBAPhc5BpW5QdSJygyG\nQk8nvRnxxHyZv8EsWKFHVRHbSK09i4KkSKgq4p3XwMZwhVCaJ/2BMwgfBsqB\n8DFPYRTZtmugAy0A3pZC/H8Ge7/EQ/I5JPC1TC5E2DgjRZSAdHpg1L8RB0cj\nv4EzenOTXc/DiEz2t5QOJJZRtClWdt12VViNXGxsrF5nakvSzBkPv1MH5QHz\n+45C\r\n=rho0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types", "engines": {"node": ">=6"}, "gitHead": "3419f94cd2fee7142c8ca00f541e3d7ceaba455a", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint", "postlint": "standard-markdown"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^12.0.1", "sinon": "^6.3.5", "dtslint": "^0.3.0", "flow-bin": "^0.84.0", "standard": "^12.0.1", "@types/node": "^10.12.0", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_6.2.0-rc1_1541108136746_0.3961860498147196", "host": "s3://npm-registry-packages"}}, "6.2.0-0": {"name": "dotenv", "version": "6.2.0-0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@6.2.0-0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "c002105343749a1cbef7f2683d850593d73c8146", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-6.2.0-0.tgz", "fileCount": 8, "integrity": "sha512-/J1ln0ykunyY838UlOKcFWEbbHu0BRA2unrw0JBMhmEXIG0bsXeGvA+QyA73LWxzY6AjkXcEYDaiBA2xVyWo/g==", "signatures": [{"sig": "MEQCIC6m1DvOYNd+KAyzqyKmfEJ+oJfuxCG/fSHffIWM1pfHAiAfws3nM97OjAlb0xATspIwM8HqLInMqWeSTGPsUmkbIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBhKBCRA9TVsSAnZWagAAmHUP/3UreeLCL38E6iSAkzKH\nmiYX1Ag8jAwyRiow00VxdH4S26Wspim2R5Lac1oQfq5PBinFtVpfV/pqdAXb\n8xQ6YsjQy4JMHO9haFHFvlLXYuhr0AG3XGKe6hFs9GLk3eponhX4Z+hI4s1F\nKENqYZliHQjGqkw1CUoGt1DcnYO950hNpLq7mf3ICHeynDOm87wWMAgtyoD9\nNgVgq9nlIx8sM/BqQ9S+Hn8b7W+Bxyn80Qaa6fKTJ6VM6opJcby6vjXp8ml5\nV7fZrzAzl6L3FwKNppP0muqg9GLinx7l/RMjAlHyeyEW1QWs51HC1pGwHtiu\nXQDWDUJHTg8Uwzp5DpFaIuHhfk6FJ8vATZeOF75Ss/PB06heCFvXvNRFY7lU\nyOy08/d01CPO4saRrDlroGCkzOcas9QPJPIhdtn3Ocga2BGuUny7p1q8yZmN\nfTO6h7sk10vn3x3lSlWDUEbcTOpRJRCmas7ET+pL/uoRL55v3B2awUXGgccp\ndyAUUvvcXT8ZU6UvV9PL6Vt9c8ihfUKRYUb1H0L14xV0R48bxi0sOnIluXnk\nakZsBew7q/Cmfaj5nbRZdLJJS1cl5sKy5Bnf/HSFhx5ZyyoQNKkoNNW+gM2v\nkHS9axDc9JPyek/sqKDt04dnrUaK2fuahPNbHWWKz2RmKBNQeIvxJZf8XjIV\nMo5+\r\n=vwE5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=6"}, "gitHead": "067bd9bdb2d9b5af02460836f8129cf7a6115bf0", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint", "postlint": "standard-markdown"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^12.0.1", "sinon": "^6.3.5", "decache": "^4.5.0", "flow-bin": "^0.84.0", "standard": "^12.0.1", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_6.2.0-0_1543901824815_0.851107586610816", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "dotenv", "version": "6.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@6.2.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "941c0410535d942c8becf28d3f357dbd9d476064", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-HygQCKUBSFl8wKQZBSemMywRWcEDNidvNbjGVyZu3nbZ8qq9ubiPoGLMdRDpfSrpkkm9BXYFkpKxxFX38o/76w==", "signatures": [{"sig": "MEQCIHj3uGpcKDaw4InI70f+DazCIJU7kY7mghAcieKCeQkuAiBumEROZwbJE/Cvv7r922YsReo5ySchvrF/gfBwJgzJEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCBjlCRA9TVsSAnZWagAAZ6QP/2whrPAs/uThS4MbIfwc\nzikQNq5D1YtFmGElNPWkncQ4G2lJ9K81o02aOzgG9Wf+2YGAmnAj8yInMldC\noytQSZKRJyUbqSK353yZChJdADBMxHQMd+egDN0lYjSV7YV2qvSsrRX5IN1e\nqe4jVHOTFj0/OlrT8pJHKDBJc2VNIJLw1j/k0pCNFxKNai+P0DZdSILekbNN\nC8fkNJSoackP/PyutKp4yspE8gQm9qXmfyGcvs+qqLAr+kG0sUTppHjJeLTp\nUm6Kj+RBw6T7Mc4d/m/LYV/8HbZH+NGcfHRCamNVXr2dHdDwDO05/4Z3MZ+n\ni/Ii2KwGfrppkSKQ1Km5mlfgWCb81NuPjvnp2GG8XX5pxZt1s9lCvsn9/Nua\nGfwdNbFMcz5GE078R3pASCXrhWWqazaWt4BKOrHMJ+MWvG2t2CW2dtYb6GCE\nZHfQtqznNHRmRy2W6if7hX2w2MsZvBGOQbFxWKn3cAwwYISGKMwrlwgt8EKS\nabPMeiNdPMQ8N51TK9aW0Sj++aU3NMt9bh82jg2bftyW5vG/6+BFA4gcOk5r\n/YjpUxApedXmTpE8wdpcw/k4gPc0PwRkUlrwtri1zkf9rn91GHgSXLM/tWCu\nLm/+C1yMYaHqdARjwZULjtmgPJdIYQiF2eKCTXQS08/ory/MBEGObb/F92/P\ntLYj\r\n=wIaC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=6"}, "gitHead": "e1e7d5767da7dac5b0b72d071a80a515d3eef405", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint", "postlint": "standard-markdown"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "sinon": "^6.3.5", "decache": "^4.5.0", "flow-bin": "^0.84.0", "standard": "^12.0.1", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_6.2.0_1544034532953_0.14992929354591333", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "dotenv", "version": "7.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@7.0.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a2be3cd52736673206e8a85fb5210eea29628e7c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-M3NhsLbV1i6HuGzBUH8vXrtxOk+tWmzWKDMbAVSUp3Zsjm7ywFeuwrUXhmhQyRK1q5B5GGy7hcXPbj3bnfZg2g==", "signatures": [{"sig": "MEYCIQCUzi/6uQe8qbPSjh28pw7ASJUOPn/LvJwsy/ZxXJbIBwIhAPw8ppJ8kFM9P8/dcW7bMM4pLLI7SOk2ruB6xEvoQr40", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJciJ36CRA9TVsSAnZWagAAMGgQAIkmjIjf7S/scwzxq/cm\nsUjZnFdYoD8dAy1MUs4WEwXGmqNBRmKaP1VKzVK20IPhCn/76mfLysbf1WbC\nnynfCLUKIKcFmKG0ZlPy4FqFaNPT/MYE9wNi5wOkFP1zBKs33ojWBolwxIEn\nbEtfJDlCpGO2ZlgtiOKHPuQMK+Nx4JABQOTjQbA6rpRY1KGdW9dQ0TWFTLEA\nhN3Q5Npm2AjU9CqQdBDWVAp5PowEUkW/E9NK/GS+OoPsxcAkBselu4fBMu0N\nDG8isRMPhe9OZT7qPOa1VE4bfhyBf6j9YB2VSyFCFQdHp8L8uSIkyylzvJTa\nbFNTvAPt6O62KEu9hLjEK6KbRbXRR5iEq4aJljahPM2ax8z21teg+U7GZADE\nyrqOrLAPIA144s3ZMjmd0gGB3Aqw1bCB55qK/y2Jnhw+wiwIzshcDPlj45QV\naDMrtCMWTqD8y3vCjtnOqHhloFNCHqRbiJfrBBv9T15ngwvtVqkiocjm75as\nwd76ZAQHbcE+mpzDqnzi0cdm1wUPrnnhMQ4A+knr9sT1/A53Ch9GgsiA2XFv\nxBudM5K7eQNaEDRMFCO1I5NBJt52C5Omyv8sbwpgQCKa33VTtzzuyT1PPb8k\nDafoZGUTKVcqPc8tMu2EPS/ShkEvaUUUF8292ETUpk8i44/t4+Ku6ooe9w/Z\n78nX\r\n=Scd3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=6"}, "gitHead": "03a891554c49915fe919c649f51b3adcae662a84", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint", "postlint": "standard-markdown"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.5.1", "sinon": "^7.2.3", "decache": "^4.5.1", "flow-bin": "^0.92.1", "standard": "^12.0.1", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_7.0.0_1552457209654_0.1627920201566122", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "dotenv", "version": "8.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.0.0", "maintainers": [{"name": "jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "ed310c165b4e8a97bb745b0a9d99c31bda566440", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.0.0.tgz", "fileCount": 8, "integrity": "sha512-30xVGqjLjiUOArT4+M5q9sYdvuR4riM6yK9wMcas9Vbp6zZa+ocC9dp6QoftuhTPhFAiLK/0C5Ni2nou/Bk8lg==", "signatures": [{"sig": "MEUCIQDJZz/uUSUCUjY5FYZ3TdYwDKp0O0zctm2xXcjeWD7DOAIgAh5IdLlCwqSooSUWf+3fw2GkOOUJ3ofrHMbxjQtSs9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcy28hCRA9TVsSAnZWagAAgvsQAJizgyImFj0f+zOv4qEi\nB1NlTqCi4Z2+Kz12WFzcTDQPNfuqZsHDGuDrOJ5mZkPeMMwNR19EAjlEgi/l\n30EtFJ1S/OXvI4Xiq3yA9DQCbZcOxta6RElOZcqVyfEDMU0ExHFPEaAlpR/h\ni8iNXq1/THOSwQnQ7pDHGs97X56rBaugY8YFmgw5OYph0UJjbOpWMBQhUHOh\n9VfvV2Ti6RHWoGDHN7DjKRFNtTZ2w6XZ7z/R3Ue+F2Pf+PXva/cihhU1ZHwu\nL88WOdYTB1CZvuOpa9WhyHpBHnSEDVOVHc9q8/ey9k3V4EUnff/WEzXGKAir\nyIbw7DrsE5isCqvMepukc2XDqnEDAXFpZLMmB3t16myai0V7l6AYtW3b7lGv\no+p5HPy+Tyuf+MN74ruO9yhVHua5LKN0ZIuJyf1vcnskglV9AAKMHL3F0PgU\na1n4Obgm7DpCbNbJ/7owV5YpZoVMEYcQQryeV0WRIbgCvgE1Vc02ED/9WVv9\n0I20pB76GlEEeybG6LyjFSUTpsTRz8iJ4I0MW+M3AAAYLJJuFFhzjlusujuV\ngYH+bLpobW4CzjsnhndKfImK1LeeeEZfkD7f4c/dEqH5w/B/RBzEpoP8ajwr\ne9a0k/a7QgdK9vZB/8ySW7PTE7Yg4lUjceB0u2QxuOXMF0dwBarhOM9z2I1o\nlaFA\r\n=HoGk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=8"}, "gitHead": "eba176b97ebcbffc3c9adb0cb5802ac03a0d32c8", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^13.1.2", "sinon": "^7.3.2", "decache": "^4.5.1", "flow-bin": "^0.98.0", "standard": "^12.0.1", "standard-version": "^5.0.2", "standard-markdown": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.0.0_1556836128801_0.004601019437348253", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "dotenv", "version": "8.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.1.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "d811e178652bfb8a1e593c6dd704ec7e90d85ea2", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.1.0.tgz", "fileCount": 8, "integrity": "sha512-GUE3gqcDCaMltj2++g6bRQ5rBJWtkWTmqmD0fo1RnnMuUqHNCt2oTPeDnS9n6fKYvlhn7AeBkb38lymBtWBQdA==", "signatures": [{"sig": "MEUCIQDHzgCzea/sqyYcYCheJMQYBAcwL2vLPFIjb9wGLsNACwIgAdOoOe8Z30ee3Hh4gGWIUyleeDeReJMjawDdUJrFQNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWMbcCRA9TVsSAnZWagAApx4P/RDg/DJ0nzX7/8uAvNEL\nnspNFRyzIaWEsmrADcRk/mmoja3G3R4zTzEObNqpxxTChNSdyfz/3lOAIu4z\npGgr49z0rDjj7B9FWyHUBNBr8S8dQAlNGhUqChaCB00nTKeYTy0RqvWxIRPN\nonWs9lyORPlnowGN8Bd51a/x06boL5iTL/3O55+uSU7Z4S339eNrsOftQTEM\nra+YFLO0n475q5iQMyDt7MI+1aE6pmCouRKrYZarQET/t9emJZ4Gn5jdtpOR\n8DYtutHbYvBJ8wdO9l4eCZ1w6I/QeUecam0Bdjzw8kDJJAmwJRh1Exzju+Cj\ncttckPX7CfyfTjOtp55cjiMgcwPn26nxUk3rGLZkvd6WwbtHJKIHzbV5/aop\nZ+rPwMcqhjzAm12PL4UZ73Zz7qom03PmpkdbvkBc1/6dY55hDeckQsabF8zi\nG8Jww4IicXVJ92Sc4arTHMKT5VFvA76jOnvMKIHC9XUJlwK6zcQo2xDFaXVy\no7zCHj9sA7UwfVM04jLrB+5gVsqsMQa+8gt3ZzQB92GtW807U33WMQMobB4i\nhdal9BnKwlq5b1qn/GULJQqDOQeQIWxsslcNCfztCcUARzHMq6ihJhNnMR3N\n6IBgp4Gf/f5z9LFRRKYwi3RVz7t9ATpu476hOJ1PaVXbwcASk9kRSnmC0CaV\ny4sL\r\n=plDG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "engines": {"node": ">=8"}, "gitHead": "349ff7bbbfafa9b920b0bd174ed03747fd3c8338", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.1", "sinon": "^7.4.1", "decache": "^4.5.1", "flow-bin": "^0.105.2", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.1.0_1566099162945_0.2772428660559656", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "dotenv", "version": "8.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.2.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "maxbeatty", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "97e619259ada750eea3e4ea3e26bceea5424b16a", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.2.0.tgz", "fileCount": 12, "integrity": "sha512-8sJ78ElpbDJBHNeBzUbUVLsqKdccaa/BXF1uPTw3GrvQTBgrQrtObr2mUrE38vzYd8cEv+m/JBfDLioYcfXoaw==", "signatures": [{"sig": "MEUCIDeUMdHjuweFYG7lvjbXYUuTu3LKDoUfTAGWQifXrsn0AiEAxvsUb8zvE/8q6+XL7nctcdtRoqnNRQaIVipBsKp/lJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpnd2CRA9TVsSAnZWagAAnK8P/RJNhxy4LSvRnWbNvk5r\nD2LJkZCMZgxxyirUwr0RQRdpSjoHISNYb6/JsUqCgEzJPcfTiCCQbLcEFm+a\nHOO4Uicp7MQ822sORW22UOL7u+AMA7oXOS3zZWWOLsjtcwYuHmE8QGFmJXe6\nFGIH05AG+PVnexopK+1Q6zVnCT/luVfHGTBXqu3fhmir/ka3Kxl2QN7fLsTh\nRzATctTAjH0sQ1bgUuXrp1r5EsY7HG4RCas/liCyqi/KVRnHtOsQsnud6ANa\nIy6jxvpR6+mwAF6Bm3CjmxOUJvirt6ghOR/+38e/uYYJWoe+sKxqq1XBIK6x\nGA3rtgPFaOO/sWMCZDsNoyeonQPIaTkmy4GFvyrjEy0Xu0GoeDucqQV0gNVS\nLEmSbUFl0G3fSzm2zfeHfzA9LvptBtud2GZVy+EfCmv/XW5B86jcMWdyE9Fu\npexATmGLm6ZY2i6KH/hCi7w23XHx07dd+jq1jlJsduyn12Ei6QcUeS1ureYF\n9IjrX7dr1gfedKj/rH3Zkn83TFagL19k4+IDDmh2j4ImGaWHX1QTXiLSUBnd\nlqZexvNPpsf+PrkdcZX1jdqS6Re8qz5MrwDWuUC1GAMyz/EZUHpSN2XdVOm3\nRPxlOveyiZW1gjDxwAgCjrF24eIgz9Cx9X8ZivORYK4RAj6qgzBbGnczgWXt\n4Bd4\r\n=LLKe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types", "engines": {"node": ">=8"}, "gitHead": "70425a9c88e5fe5c3bd128fa973701279a76a9e3", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "maxbeatty", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.2.0_1571190645866_0.7378423275144708", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "dotenv", "version": "8.3.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.3.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0f165f76e77769278d95e634838eed97a27ce5c2", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.3.0.tgz", "fileCount": 13, "integrity": "sha512-1Rzx4VAJIy9LPqtNO21bBdP8A6WAXcTlnLKt92o/vZ8MezXcjjzb1b39Ls0BWSIcW9ijkFtUmjOGrwHiqdb3Cw==", "signatures": [{"sig": "MEQCIBHDvAwU9MGIaHfQ037ptLwrnOfAIZhAzSelC9dGtSEuAiB/j/+Y/uSGngHRonamcmyhiNs4TbsCJovLpqfrHa7A5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkgr9CRA9TVsSAnZWagAAUpUQAILX2gR9KY5Jd+wI/1F4\nkLasmDltRcaFWHZegIhKM23GxOY9I5vqA65MvPP8iKpG5doj50macmFOS+fn\nDn0K/mtKGAI8JsROFgX61puPgdKT1MigT3YkKwzHOihDXsLLbfIhjjrhaz1x\n6A/tS5VhL8GejSbyQgRM7ORWBxfZVKmTI+hmI/UJQ/LBChy0Ooafz2dSRdEc\nnFGws0XxcIajpO3FYMzwe20UpEhm1+HDdTtuAzKUsx99OHC3V2/DpzgnE5sg\npzQ9HqjcqUsTIielhZ5zexKGjeDZ5Yu6yc3SwgCtrq/PQDYd4ROxhE1kTdIi\ntlMwUfD6ev+uS+XYM7fNJmrROunogJnjS1jRA6LCe8c7ycra9JzL6zid9rt6\navPliA3Sd8uwzXHFVhcpzfiVhC6H7+8UCnxZV7rXKwJiFe0m9c1moxEfQoRl\n2W41OiFEypcQcj0EmDrotassyvAN8TnR2QlgFiys2mv+6qc4xFjVwzOdTW0P\n65aF6Hf5M9sKwSr7JiGTOhGan8NT9VXFkyWnZcRWPnRpE4Y6YFFUTDrud1rb\nzi8KeD14tAOuh6vbi2B6FN65lH/XrgGv+mcMUnTFCTyyX+bksJjE/fK3Ncn1\nYiHH/lSTClZ43JD3XTT8NbdZqKQqvUjq3VIuFrLrOQNvwVD//K+Wu1tuXD0B\nswgt\r\n=9bO/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types", "engines": {"node": ">=10"}, "gitHead": "db30e1eea7bc054fdb0f7750684a2f016a9d21a9", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.3.0_1620183804710_0.6312539150040126", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "dotenv", "version": "8.4.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.4.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "08576a9d5dc63b4fc58df087015c768eb102e0f3", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.4.0.tgz", "fileCount": 13, "integrity": "sha512-l+pIWjvRva0AhnLerv9VvpscgXa72iPW1qKlCgA7COzJA414vGQ/PMcOuNfR1CmQbK208WWYeVwQUa8yfuqH8Q==", "signatures": [{"sig": "MEYCIQDgpmoMnjB3nNx+bJt6es8igjvXULlbYxDvW05gzLHcVQIhAPnhKxuBgj8WCJilR8hpF8KYL5ys9FQUyyzdbIaBOuvQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkhLmCRA9TVsSAnZWagAAzPUP/js7MmuuE4S3xz7JwBmv\nRGLUWSIH/tez0+qVRPYuOznsXLr09U1dvkWW4U0xNVQmaIWBl4WpwwrCm3iV\nKTvJmxpiPnGIAtzFYdI9Ovdi0Ihj4Uttxv6dCsBf4t+AgFzsDxXYeSMKtU0v\n0pZd4w59ri3DJXuPkehpFHp01/tmAZKexlKto2BJAlUWxtONCyj8bUESzt0o\nRJTrgqwg4K6QbQPyfosdIzsokhLj6QpVko/HoWNSremsPpKuJ7na+UTMRqNM\nslPdlsN10PIq5Z6t6rj/e1hnDGpVQu88W8isfzIwXqL2xwJmdxke3M3NTPnu\nTTWGrtNH0CdfmAWk17BmUdRfVhvBYQNbX1aH9xmgkHdTacFYTUGgTTBJHDhj\nlKT5IV742Zsn55VX/SMeBZFzkQADRDIbkuND5WVWE/tnlFfYE5P8zII2FtHo\n+p00FyFnFprFcxy1CCLRmI0Dm8PbVItA/sEEq79WRX2KFtXOpAkuADCBehW3\ng9GJJzctkdqTGMF0JwBhA6LvnIKoaB4kcjlytb4t0qzy5Mxp+feAJ1lm3Vcw\nRz1kOMeW1i1z2YCoaCnvEiNvJffC9ulrxLpY6REcAnERR5wcnqXdNQe42rX7\nbh1NeA74hPUvERmKt2MTtFrhJVIzP98ndpicsIF6JrLCuxZ2Turi75uGZf7w\nnXvw\r\n=2X1u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "gitHead": "26ba7153b4ca44c78be34f81c7243b05c19dd9aa", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.4.0_1620185829948_0.24208332019945344", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "dotenv", "version": "8.5.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.5.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "8169c05469a60ccd9e8bd81056438db46312a021", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.5.0.tgz", "fileCount": 13, "integrity": "sha512-vRlOiNG7p4iPlnX4wgjPbmhnHDOoUH+TgbGOC09de/lwz/Iv1OK1Gj2qC3+cW2V32uN4KFU6mTLyYsvSAHHMCw==", "signatures": [{"sig": "MEQCICqL2cmQLKhf4kJRys6QauVjW9rFeuA8SLub3H9YSg4WAiBzKupY9QEnsTPOC0d7oouCjc3Sm2JP2AyU7rGf05R8XQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkjWcCRA9TVsSAnZWagAA+uMP/A0Be1lnRRBKGyetOn3V\ncZxb6qLbZu9hv9Sv/JcOKOa4vwswnypCHCP34vsKF9kwaka45b8t4sd5UfyI\ntwZQXUJ69Qh+uEUtN3TkEs/022PQ5ct6ioGSf7R/Snlnf0AC4zARsygHJMTn\nxgCG88ieePT/lhI2oYvXf/y+vl7RHuieLqmU2vM6kvnSK24gQTYMbMR7oLOH\nfjOUEh+oPFmb6I+xZtp7ikEE2IIc8TTt/kow939QN/OwbcHeOAMi0/iwgHU/\nQIHEFwEjAdsDJNgRX1O/6U7msU1PHgmJosv8MLuwuziDnPcfaaOA1IAyqyKG\nLUcB52NL9pC9NMbijiOTUwpHxvdmz1XOEaHw55mbYpIZvCdERVKlumsiuu1f\nlpqb6tgBNORcWyi2w1AB6umVmcm7fViCXjxs5ovsywalz6XXtCPGOEUB2WAd\nrIVzSI9VM0PUtJba8rJcAHyZurttVrXqprNJ9AzNwpql/aO6bgVD/nd7+S4S\nIObaQ2cR3HdRgYjk3ZWYxb2vpGx12c1tBLB1hft/oDfdKyvFOc37N/6hSTWo\nrCVT67cMcz1kkCe3I0Z7/tfmD1HX9+0A5jO5QdJWFCctdRA1hufTkvPQohgb\nCLHbKma4YwyWLPjj4WSF+njfixPqLuVE4O8LPOVcsdwDTg0BJeIFdAF+ckvT\nkxL7\r\n=jqnA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js"}, "gitHead": "9d9038f4aa3665f599a79ef0e6c53a9db2844b83", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.5.0_1620194716244_0.32701290625755197", "host": "s3://npm-registry-packages"}}, "8.5.1": {"name": "dotenv", "version": "8.5.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.5.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "e3a4c7862daba51b92bce0da5c349f11faa28663", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.5.1.tgz", "fileCount": 13, "integrity": "sha512-qC1FbhCH7UH7B+BcRNUDhAk04d/n+tnGGB1ctwndZkVFeehYJOn39pRWWzmdzpFqImyX1KB8tO0DCHLf8yRaYQ==", "signatures": [{"sig": "MEYCIQCOYoEuO1wLzGVyfixzBQyaTA2jHOSb2xV9y6rD90a8hgIhAMyh2VuEakWYVBd8hbZYt0m5+6pE2lXqgiy4APQ7An42", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkj5ECRA9TVsSAnZWagAAsoUP/izz0AWwrDp4jyPC5g5w\nV0xbgzM9VocCHBiWDktHma4hPRdwdkfS2ImMQmA1fZD2t/LgpPEYUWwQT7QA\nmZnFwkNRg3dUYh9zVoM471Js/i21qj4bs/40rf8N7FeXzeQDPgkiijMKJ3DA\nXfAQoERFwAkhqKWs7jeT8kfT8zHfbb4GHsmUR5WOtxQGL2H4lGvx3DhTUPS1\nQGYfXG3/lgvZlt3fnv88Raib06ZX80NThiD1NthjM31LLnODR/9cOu8dqbar\n2aqJVsM3ECLYjbQgfiiUb/TrU0HkRlCWUkKF5m2VOzVREmf4VT42Pk6aDucK\ntdDgYNrdA2AeaEnfzpmxy7ak2vTQlgWpthGD7gj+x1po4kPf6B1dDzKRZKeH\nebE0DLvz/CFZcbxo1CVd3w0gaGxWufYgDJOPpEwCYCmvQVw3R6uGpUt57jSS\nmEMBSfl0uwjBF3ERadHq2wy2/pg+Ep/piPW2ZSAPdT9nhwpzjw/Rkh/nGNZU\nWQzWee+Kt5L+XRSJN3E1XDNOo5Ak9nR8Bb3ll+WE3Ao5EWxfFtY+n0fZOWEe\nHsvU/xk0wC1egJWxuNJL77ijSwilaZvOmwH5726rxAePh9lq4rO81ZM+UrMB\nGrWf1zIshZ8cuctHAiRJYuo0cKXAqBZHuxWXPOA6rVBPsTC0xwofNW8SFmnj\nWKVQ\r\n=hlbg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js"}, "gitHead": "a74c220a5bbf4e4355f090204f772b9769a21b13", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.5.1_1620196932294_0.6508414245289351", "host": "s3://npm-registry-packages"}}, "8.6.0": {"name": "dotenv", "version": "8.6.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@8.6.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "061af664d19f7f4d8fc6e4ff9b584ce237adcb8b", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-8.6.0.tgz", "fileCount": 13, "integrity": "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g==", "signatures": [{"sig": "MEYCIQCMsUHl9n3BvEhC9DMHaogY93zKbv1SyQVtYld1SuQ5kAIhAI121Q4YvRP8G40jJgRrwqnX1+i4t1v5TMK8iC5kTp6N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkrzsCRA9TVsSAnZWagAA5bwP/3npYOpXQQw41fpv7t7C\nuockZb7nMp745FKG8IZO4t91up4jEEXu0HqfEyXmc4WMut9C5DEsNrpeUCCK\n2yAmnZ8eiPLWWJc4c7rF0ocR0/QkIlmwMAviKIvvphtsS3uEnqdlQ97lEA8U\noi/Z9KgstQ7X4KJc+HLXIt92+HHiJy2Y8owbCWo8RBTVN4pDPog5gDuRGY63\nGVt4bKOKwKdubqCaZUTZVa+HBdTBzm7BI77ZCdrWRkg0g0q/0YEmMRL7jyYM\nNYTKJ1EOo7V39ErVMstIJJB9ZYwoXiLmU9BH6oSvT60pNAYeBz5G+moG/JiR\n4FKdAmglq8yBukfm/SOJtO1VZNKdb3KFx8Ik83O1K3A7hcSO4QIy1/Cv6jmq\nJzoTUAlOZa870ZkJYCf4XByv58IVaQwpC3oB2ecoRBMzTP7I4ZTbIQHqCzcb\nlFvNVvlcbfjK6IhDULms5OCRnVVT9hVw5vNpa5a5SAwk19H/0daLoYesDpSm\n6W67rRynLU2XWaipzhOrwyzibcgE1IVM65mPyWOYgGNbK+j9+8yJlIwHvOid\nsg9BIIM0flYkL23gVsJO4zTC0/ZJ1gH9r/Hsu7ROKP9ZoaHaYFwsrZxOOXpq\nLuDCS4toYIv8aM0wI/kxkoTBCa+rU5MmTgF4BuyzH3nYS6R496vXrhVgZYru\nB1oh\r\n=KJiL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./package.json": "./package.json"}, "gitHead": "f7f7df4ff2de97f39d22de8170e33666bdb69338", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_8.6.0_1620229356449_0.924101339098544", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "dotenv", "version": "9.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@9.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "2408af4e39378375b7eebe30b7b54989e36d6e46", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-9.0.0.tgz", "fileCount": 13, "integrity": "sha512-yy3x9XjojW8ROTBePD25AcMoHqGHsvHmtfw8QWlpEXyMMXXPj6brUA464AptUvHuTPRmNz6Sd3ZLNLeJl6dHJA==", "signatures": [{"sig": "MEQCIBntnpwjkIhzUCASVZaZsaP30z9nM1JvH/LHd5tZ5csWAiAK6twpVZWA1PK7RNBR3PKtnc/rH+i9cAmd+lW0UfA5pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgktKjCRA9TVsSAnZWagAAWkQP+wXQe0p9hN1RQH7OUYf0\nq1UOkngVA+sDPF2Rq/8RVJE3pjJvBKGyD90g2BrJ/XFFFdUg2qAZtgpx6jAm\nxuRWxg06cN5GkX4WelJLGWx8ESpLc1W7TY+UXDFo84M1PdkQuqZtxPOsoC+F\nrhqHC6EuzY0uzkE+oCjw6gFG61T7hDEH8avTJYaUMzLmCVpTrx9d7rOd7x+z\nGDwt2tXw9/ACnTW90RYLxGZc8iyEC3uDWn0icGYCu0YrqDh18dsW3ZRK9xOD\nvxB5+4CW8EJozK+NIrE2xQWXKtjLnJfZww6uBV2x84Bk76f2FiWUL4JsPpm0\nDW8v3qKRF91rnW5SaSGkigOQSbnvh2kTSjhHtAfmC5TOL8rcWRHeRvUA6wRz\nNy3OszDdATXXwpxTnSd28+rLw/Y1aJHymXLyo/cS+lisNai3s9eWEVD22Ipc\nZ/abcg+1sIRl7rshMb61l41dbBdQUl4TZBf0AbBRT8vOIY1mw6EoiDJeneIw\n1H4Gnc2oqFCN8kuIZzrtN0Jv72sUkPg0GNO5RnjgvhbL2rRwxJxTBiLucoey\nro2l+stJpRhbLi+YsYCfEFm7+FSU3XcapW4bblzr9k0lcTY5fYXivu64wdhy\npQoEbAjBeOFuzY1FwMoRG0gJj5b/9xkr+5weBvqzQhaRpLKZUyss8dYbv5GR\nZnxm\r\n=fVuv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./package.json": "./package.json"}, "gitHead": "3214bf46ebef0d2d48be2bddc196808e336c5940", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_9.0.0_1620234914866_0.8666277446100914", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "dotenv", "version": "9.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@9.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a889a28a3a515812dde1e7f8183ef5cdf3186b97", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-9.0.1.tgz", "fileCount": 13, "integrity": "sha512-W8FNeNnnvJoYfgkFRKzp8kTgz0T2YY4TJ9xy1Ma0hSebPTK8iquRtpG12TUrSTX5zIN9D/wSLEEuI+Ad35tlyw==", "signatures": [{"sig": "MEQCIHFQhIERQkjXK72d+sX8DDo3Pvd6fp87SHXS4lqSXZb6AiBLPx19EvAip14wB6nCBlWM5aOhrtTOYf1mKYdeye4AaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgl3/oCRA9TVsSAnZWagAAoKEP/1XVnxQHo5AhLyx6iCSM\nZyyn9YsqVk7fQ0ZBKjfzn0HQ+lz2Lqvm0rL7BE+SdW0Krq1OvmhLji2BHPAm\njVOuWlq82+V1cvYyLIx8CWiSSy1uo1xPCYQfLrzHKidss5hGINLbWALqqZKx\naESpxi6thAtKqCMlZ6Q91tJBbUgzMTdgEMXu6inCWE9nr84O9w29dYMZXjPj\nzSe84ZMnEg8hJqNsYAFIYKnkQFrhIM0zZdbWCxqrBg7ZrrqdPdxZR0CM94m0\nuLP+pBzhMlXtbExOxq3CAyxLx4nYhYJYyrrfRhHbHdU106TFAEolQfSlE49t\nRhr8M3XXvLnQo9xe68BzRArSgVyzZhY6tb9s/5dqat/m3v3Ed6qJ4kKzLOqy\nYFdtoXgOCfcBBzdUZjKBjBkly6CIbCNrioCP/TRoJpATEdmBxIVovKaOmYEJ\nKAFPAU45rvAeHOikiOsM8vqiJffEjSypS+jV8E/g8M5GwrRl3wDH6p3IWN+G\na4zPH0Ew6yK2fO3KlIQ5duJWzNitEg4HOkh+rEw5xgnClU9A8X1Tgl5GNP3W\nQBbXqR+WsbBD/rdpOXs6qbzHaAf66qI2YgO8x16D1rFuFRkLnHmksHbpy5MS\nKm4QJtHmqg6g9l9YhyEEfaaPQNjKi/4/wr0OhSE8eVAgDP8OlmdikveMKzFq\nOkkF\r\n=XS4W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./package.json": "./package.json"}, "gitHead": "5dfa02a7ae6b5fa0fda0ce0520c3804cec4ba9aa", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_9.0.1_1620541415950_0.3228144609225403", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "dotenv", "version": "9.0.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@9.0.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "dacc20160935a37dea6364aa1bef819fb9b6ab05", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-9.0.2.tgz", "fileCount": 13, "integrity": "sha512-I9OvvrHp4pIARv4+x9iuewrWycX6CcZtoAu1XrzPxc5UygMJXJZYmBsynku8IkrJwgypE5DGNjDPmPRhDCptUg==", "signatures": [{"sig": "MEYCIQCJWhVopKBvLD9qZDZ27saaXGH2xLn/FmqH5ZjUr/Q/nwIhAMayfPq5E0jWqPbODUnluYWTueBFb798jIb9i6raY/dc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmXr/CRA9TVsSAnZWagAA+5MP/RLCtnFmjX26HcgSpb4c\ni2ae/plulIwx6P1f3FFH3WXenPeh8h/zMATpEDe7PjMPgZc/6t0A0R/d0SYY\nvOB2VKaJKGMlsTjVZmYDBWf5RympLmewRibkQbOPyTlNc32grSszVvom7xGG\n1HQ0HNWAjAKFMBd6eI7p+vBuwOFmszsse2b2Oy3LVYzbB3DgT8u6CCwycJ/d\ngXyW1YuW4MBmWG4yZsCG7lK5y9Gn567sae7hd70bwYaPkB72L8fySXNQ06AO\nbgZtOD/I2E0LYRFrfeJcbrFEGo3lkDA0MJSbNe6RScJ3uMVdmVK5slESpUNl\n0AtSybnoabQke/mx/xvUZ0QJC0xXW7EAKtk5tluBkrsneiQwz9nQ7NmzKn8W\n9T3RjF9VkYZXoLI9oLLdCrQEWg2ienM7rS7jhNH+66iavRJs9WCUrH+goWs5\nkNxW8fZaXa/YDvfooWdNR0rYdSHS/XkmwMWgcofHnaVpRdLaf3VIt1GT1JaN\n3VM+RjiJ56dyy77LYx2LSCgRbGcJa68P8FENpNSQA2M88TahVvneb/tJYFYF\nX6LWwVd68AtLKLSNNIe4R5Qjiis40RgeD0EyRXE0At/Ey831ky9bjM/Pa5dv\naR/6J/AVqfAVLkU1Wcaz8t3/ffXoMF9McTgiLuh4GgyHbGa9FF2tu+FWzLhY\nu9Kr\r\n=cw7p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./package.json": "./package.json"}, "gitHead": "b0bf66367362a5f18f6628d25f997b30f3205d0a", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_9.0.2_1620671231381_0.23441784977208657", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "dotenv", "version": "10.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@10.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "3d4227b8fb95f81096cdd2b66653fb2c7085ba81", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz", "fileCount": 12, "integrity": "sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==", "signatures": [{"sig": "MEUCIQCIx2wN8CB2gITaSnV+DFxPfrTn1bgWrkNJrlnmm98mrAIgRjPnlE/Pxa8inTFoYm+IVHkrW3GSmqLm3X1DnB8hkTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqAypCRA9TVsSAnZWagAAl78P/3kWrugNWJd76frUaYsT\nE/9dr7Afd3QcPWTJkWHB9mMCgq+xCK8PVw0OD1rFWkQTkMVzzo/LEeDvGtCA\nbDlz07BXCGyWoaFZeBVL60zTbcywtl2DaBzA9a83UwnWdwt9AKbN5tZpyJqq\nJF/KEt/PFsdZID2GPIpeQfAgcVjURKESJuRCoMkN1L2DVbJrktgQg1P2vj4r\nTWv5dVBQ4n1Se3b+RDnuXexpN7iKdVk8WuZM9D5DsaYutKdILpxvM6VO+MMQ\n/lNNLkZKj6xt50ygHU7aEUi//vTsv0MDrMSQk9xf8RgUb5V4Xik2uhs8DPqJ\naAA/XXJktyRQNPey8Hf2YnSbaP9k6fkdwO6dZWaMbrO4hWY344Gcp2yVdZU/\nmWuHCoIMeIlg1GmZFhnT5hWiTWLPTT9W3cHaF7KkNu2QEwNeSWRJ/928JrwW\nEzYXEAPZePT2ce9aQZzobx4Nr3b3dbwxKcMlmxFvKXUDX2Y1udjbHGvA8S5K\naaJVrGRnSXf6sIAL5zA95iujFmXqTFLnQFVfliC9sbVEPeMFG5wHHiL/FzOP\n0Nw1RMyjEck8ZOO8TboxMlf5sOEtFlZUxvBvd4Rre4ZmGyN2+YUQOQSA731l\nw5ZXmrXBKlz5uWglGCfOSfQFC+Zm4meDTw2iuBErQmOe+PLz8a00qxescwTP\nsW/v\r\n=rslT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "8c0c22719de756fe15f6c514d4599f5ed25faf18", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_10.0.0_1621626025196_0.9341824352141679", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "dotenv", "version": "11.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@11.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "ee37feddf8ada6d348a79e198312d4a8abfd1c1e", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-11.0.0.tgz", "fileCount": 12, "integrity": "sha512-Fp/b504Y5W+e+FpCxTFMUZ7ZEQkQYF0rx+KZtmwixJxGQbLHrhCwo3FjZgNC8vIfrSi29PABNbMoCGD9YoiXbQ==", "signatures": [{"sig": "MEUCIH9Qz+WUC31vOgzX+dyJR5FyTOJ1UzzVVPEO5RCCvD2kAiEA8yp85vDzdIXzNpWVPVeKP+ntWAFGCJ9tfRT7bbtB7qE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3SrvCRA9TVsSAnZWagAAYRsP/RzcfuJPaYamAcSWAKjC\npS4YshYOtK8JMSDFAs9/ALdBc/armGMayEf5Lvd/6UWxrghPBOvmN/e2exxV\nmcFR2yzi2J1NHkInetpsS0fnPupctHc4dQwW19NoGpDLtr/ceTSqH9/r8V04\nSoMp3MBrMII2Tbv2tRDlvtAA52e/47cNTFJZQBudoa6QrZWxPfccys5qiNXv\nqRBTB4M4xy9ffOSdMO6GUzk1e8WTSl4YW3vnxVAPOjGc1uAma1WteSY0+lph\nXWc/m+sGgtzfYWcxAvGt9JkGTqOCVoxyH1UBGPfYHhVGrviPi5+ofTX7d0x0\ntxHiQqMh0bp2wyb1S84ldSME4SNqTqYDJYV60Tr/Rlda3LVjPLQpkCPeH7Mz\nxZfPoLyP3UjthgMnGy1WJBBxer5zyjbEjGuPRjoUKbfaBP9r839xT8Rq38Sg\nAbQyiErbLCsxgnYUdzJntA1XLYHxU0zFDHvadp9+rUsTysO0KrxQG5Yf7xxT\ntdbE/iGjj84uASqC5hF440ZXXQr1u/zpjX5Vx2OL4lgXNp64Zk371v5wT+xy\nwuFijHIRPgAyIuyim4irzW1TyN/FqAk3JRkIuWUDs+IcwkWWaEdk+x7Eh84h\n94ChnNMdVC70/hdL+ehYv5dVUKDQokg1G0dKQWU/EG7kkC9XkoUaPpNAjqow\ngk85\r\n=QF7f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "types/index.d.ts", "engines": {"node": ">=12"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "bb8ee10406eca388d34c85bcca4a08a96f2f7f20", "scripts": {"flow": "flow", "lint": "standard", "test": "tap tests/*.js --100", "dtslint": "dtslint types", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "postlint": "standard-markdown", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": ["flow-typed/"]}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.7.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "standard": "^13.1.0", "standard-version": "^7.0.0", "standard-markdown": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_11.0.0_1641884398887_0.3684952598143605", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "dotenv", "version": "12.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@12.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "d89e671089d6951ab54d638cfff7cc3b211353e3", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-12.0.0.tgz", "fileCount": 9, "integrity": "sha512-XtWJhnSVnpOAokN5N2LFLdFgSz+VgaX2e9jbQwHBXpG2toGCBf8dgfBy5TZWkn/YwUXlwBounogLH2myzW+Xbw==", "signatures": [{"sig": "MEUCIQDuVT14uomrcaQ6ZCCauvErz6jDX8D8kODCQ5uh9GmRNwIgZmNbmlUzo5BVkItgrbwC9NbDNEHzKaaJjJYNxnKre0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4g2WCRA9TVsSAnZWagAAVtYP/0Tl/ycLJ8iayyG98b6S\nrzA5h/5vmRBDUcQfBmpdGdoDuuFzVLHqjsxDAjzRG8nyDI6pJ5sCGp7Itjuo\nWO/hFh03HchEGqeoTiUWQw6t9eeAK5PMOw+Ozp4nXIFqgnWyiSVaO1l5KYCv\n8gxHvXCQGzQ8AaEQd9aqv9X0bRrJmJXZlnzgoyk7eLdSJLYqS7QV4C3+NF2h\nUZi64hqzZSyNmjvZainziukIyeRqYcTBZp+H94yQkcWjbVpXhUo5cC0fy9C2\nCHlPb7D0sGGL5caD1v9I4otxtfVZUp8ppt2jfdJGgBrFE4+YsuXHfC8vEJfF\noSS2TbVfi4f2dBSGTf3OKltUA0UFtdoO+UdTjSqLda0/+hmx3ea7/+W558/Q\njvO8Ya6cWfSDhZDyDg7SubMZ3Xa2tqrwbE9S25NyiRJs5cJbpvzUxqlsj1md\nXjDmyk9njln/vUigmvAbsZFmVZshaVfdfOGrPnl1t1XoiPGqYEpwGRgdzvDN\nJkxmYFcuov6SvBnUrNuht5LqKltuPvrvm7YA/yPpxpEtmmJeclEdwh5D2sMS\npKRLiwzQS4Fja17RaJZUXj5I7bG2x2ERaXTN2psM7vtmUooVcV5itkIravmt\nnVaXfLFcUpLrtW96Atmw7CAm02AijpTpKWhlS8f6OvIKvZKyt0ZsvJHJ5pej\nbjdk\r\n=nn57\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "3ef8255d06c5001ef20f8830eaeb358382732abc", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "postlint": "standard-markdown", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "standard": {"ignore": []}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_12.0.0_1642204566165_0.05720460129561267", "host": "s3://npm-registry-packages"}}, "12.0.1": {"name": "dotenv", "version": "12.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@12.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "7cd153a503083214381f5365880c6761daab8ab9", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-12.0.1.tgz", "fileCount": 9, "integrity": "sha512-y7t4pLtT2AzgTfmjN5JZXFzDCpYRrqIqPba3OkxngiC1CsibsTcUs86nF7KMmCt1/fYVK1a3VyZ5jnvrADWpWw==", "signatures": [{"sig": "MEUCIQCccjGy9ELzwU98g4Cuv7VzwjAx71h3MD/9nwVnPyrR4wIgDJYwky1hc+3t4H0e7jJvV9KkF66aV2ycIXStNhj8sa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4hpsCRA9TVsSAnZWagAAk7oP/jmrYTM7B8zVgf6RVgK1\nG+T8SqrAun8ZzeWMmUJU4W+iRR5Vz4o5YyiHKQM1u9JxhpeV4k1noF4ZFKiv\nLjDB90oF1P9YALH8lFKr9dd2ZhKJxrZ38kWZUqRHhIl0jORn2CDu5c1G9sg9\nzbyBfEDOh9iXJHe+ZQVRogQjrqs/c8A/iXsoZ9VP3KEgFBIjvuIZiaTNIpX9\nKjCrrT+MZDku9UP24dfm0DW0QSjcnk1+pCmM70dM1iSODxC+A8C80v30FV5r\nOpmX8kxCb91p4URGIPBhgQeIlyLBJcJ1dUtRtAXcAWC4sUokFnnQkFj1EQSY\nMd644OyykJj+oxLJxTqDY9LauIr5syIEXFawLjvfUkFf3ZeM9dpWHIhcOOoi\nHR4vlhMNY9ASu0BQ78tECqlaUAgChAkwtEcdVmOoQsdopPMVLEje1/2QkwRC\ntfnwqrZcXpvb7dPxTS3UNuvNuLvVXpC9wnfpcpk4oKVp9tJaJ5WCmEPxcXby\nx4pXCw3XcdOLI5mNkG07/ffsbUn9BzqrlWZ01muFDK9BQtHnqHpk/6d2Vj3V\nU/TF7y2W3AcVBxxTZ4hciBPcyFZ0eQ6hPt5maM896UtC5hMZr8QcGLG/ysv0\nrw0XXBV/uUMrI/ppQe0ipuneeVI5MfWhRlLJT176oRtcabLSu6H49tC/W0+B\nB4Df\r\n=M6pb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "dd4079ac0adb993c960bc77fa06b9e4ae6cc7c6f", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_12.0.1_1642207852329_0.09191158501128238", "host": "s3://npm-registry-packages"}}, "12.0.2": {"name": "dotenv", "version": "12.0.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@12.0.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "1c1958359853f78846d58990a18059f66c280151", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-12.0.2.tgz", "fileCount": 9, "integrity": "sha512-3LvZ6OkZvE5yaWiFPd0lwzL6W1psHlrJJdl+BpEAzuh+sATBo24pqmKTUyJsiaPI9qLbPjuBxkBhoFDowjGd3g==", "signatures": [{"sig": "MEUCICBVOFYhd5W8a36vZwXPGYy/mBupyNQYH014/eNCBSMKAiEA0CpnZdLkUnVUWDJHwUABlZQLkJYokOoKEy4txWRlePI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4iG4CRA9TVsSAnZWagAAceIQAJGg5+9Fu1Pcx4Ap3Z6V\nIAsdViQ8Q7EKxHAD1SMszonaI5P1sOw53WgmHkhFmlFxQhZi4JM+AUQhpXQt\nTeRPsjoTwIWv2jNWNavDpEEItFyAPqokOh5dZGHmGmOX1KKRZOcGC9K+5qXP\n/lUEOWtRpyn6JONFdUJ0pfqp4+HKnmU1Dr9kOsuFv9+6ec//GX9cwX9L88Gg\n9JXCrs+rr+GfaHZYB942UE7THEWryW0MQZj/aLj1M17w2jn+xKZjwyWpcImS\nBXoffsrsuV5TT3r0hjKl4lP1UwXeonocHB/TSWlXJiC8j4acfiuaMzkq5tf0\nyT7NOIJWxvKV346x5qVAWNejGEIEYBEjECSBkShtP4lj2dEHW0iqUxPFZhkH\nnF88aNPfy7/RM9CglDkKAQglyRvGStuiMh3yqhkGU3tL4tv+7Z3L5zIzfyjz\n2CcjiXQEUsnwf728iv8nEHqF2gHkZEEdVxSYZs1C2vWb5b4Bdk5YBioGSrEQ\nuVxSOk7L8OIUYP7J/F6Wm6ewtqxjKA9sHyjGqZNBb/pzvwR0/CgTF58cNr2k\n8ngoYNvwL6iW6PHzdqIw0GPogn17iU4SYzqcFHQG+FsPyb/ktuCiL0bkkoJa\n9GQvYez5c/wZ4G1uW5l6QRCS1rjKLQXgPSbdwxbfHX2K6q622l5uAoTD2eQ2\nCe2S\r\n=5RMr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "2afa291398a0e8818b17f2942650630e949a502a", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_12.0.2_1642209720029_0.362615845892845", "host": "s3://npm-registry-packages"}}, "12.0.3": {"name": "dotenv", "version": "12.0.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@12.0.3", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "362b83f866dfed8e540deaa5bbcb3c8ab401dcb6", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-12.0.3.tgz", "fileCount": 9, "integrity": "sha512-RlOysyzyGiABgDFgITo5fRnMV1QStdoVvkXhnxQc5Or/CiQo52s2Bh6DVMvzZsmFQ9IrNxYHOC/gdpNwHsitnQ==", "signatures": [{"sig": "MEQCIFJCXLXYTLFD3Bzv7crYnRjOn+q+PykFvOo7Es75EoHvAiBuDQORAmlTMM8iwHgNfnvzi6PxZHUv06MD6Y5wMWTz3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4iUtCRA9TVsSAnZWagAAR8QP/A7bA/sXPNwI2AZsMZFK\no4BzsSAfJupxRp9IcZGH5sfVGsduorJ317FlL8WaNdvLSX75jNQ/H+OsaM3v\n71wBzPH6bVcuWJopRdU+happemxV6L/H5jdt5nZvsSk60CtobkNPn1sAFPUy\n3QZho6FD8Q7Gwxn3Em3defi5LDtXS1zcBz19PmSuAD/2AKmFEZB4HFsiPYAZ\nUvdqI0tzw3sXk/FScbJgJyU9Xb3wfzIpHa2ds8tIJerNja31HKITkD0Em6Lx\n+LmOYXU8aYdg8HHvXH6xaVPNHBGw6UvAGBQxl8bFF40JepozmMJo0nxPECle\nU0DlnYAhdhSxe3NSWzoL1pyu5GJhG0D3FNNfIseWAtULsxHJaUc4sedS9QZN\nyijBOqS8QQFMqWPctrZYNxBSxMFSIU5jJKHCw9+s0xzgYKOUmC6Fx5m2VxOk\nsv78TY1gIZDsKZj9dQ+OaykxCQJjEC6gdZWtHJeUpS+HL/YDIOsy+QPLPXdR\nok/r1Y+x78Ty4x5EL9/wLCJtWdLnA1IvolkQX36YC9+g4SoYTDCWuNKAkM/F\nOVcq1Y5mDT7MOgSuZ6T24nBxKpe17guKkl+xAC0D0xrfodAYiUpvHG6EP9mo\nkITmgA2uTPmEBtbADjoo3VcPWD33Gmn9urS63P5hR/0Cfh9pQIfYJi3ypZu4\n8hoT\r\n=Lw5O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "b6528727dbc30ec46bc045060b41d630a0c96963", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_12.0.3_1642210605374_0.49022334231685494", "host": "s3://npm-registry-packages"}}, "12.0.4": {"name": "dotenv", "version": "12.0.4", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@12.0.4", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "87e302cfddeef475fcaf9a617f7b44f80ac555bc", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-12.0.4.tgz", "fileCount": 9, "integrity": "sha512-oWdqbSywffzH1l4WXKPHWA0TWYpqp7IyLfqjipT4upoIFS0HPMqtNotykQpD4iIg0BqtNmdgPCh2WMvMt7yTiw==", "signatures": [{"sig": "MEQCIDCSKRk35y2XLAHkqck6AEPoldnKS+0Zh8NH2OqPyMsTAiA3rnV6CHXR3SJWGUG0n5paU5y8wOucQpOKOxfhRXhHIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh49xxCRA9TVsSAnZWagAAlS4P/jQABqRKGRvBpSgnSvjb\ngGblgYPRbgw5kZLoJgwzVOBooLGeH8Xq4FJGYj0LSms+9SzNqUNcNLlZGz8z\nq9D+/LAzFPejRx6OM05jOv+8tsndkRF9AQvQgVL2cu3QHQ8NCvhACpgZJ1Pj\nQJerTL1sgcdoBRS9402yZj+tCW6UWQpZeLbQxP+dXSl59MqmV5CNnsCRu4tR\nqXxVdaqH18VTqQyCogaWe/x+AOB8ciiizIZfTG6y+kTims7H8DOn4VMW9D0j\nGy6qvsDNkEHs2/AtFfBNODM01P5KXydj7viGagouV/lxoevgvSdvU0Ow4log\nhKIdYMd6OAWYWLFUs+734Gk8ixc4yDi1VBIAMTpATu9Fq+9yif5iyqOZv3n2\nVPL4oS3Y8da3AmMqbOUZn6Kq443RCA8SEAx0Ex4rFGGABO3LRTFbgiV1Dnc/\nkksotkvszScqGaeYMJ2AfVwU+63o6QBEgDgk55Q2FayCsyz+U5kI40NdAuMF\nZKhPSQQekRNMEEs/SS6aVWGFQdpo6wVDTFu6eAPucbKuw6JF97IHiMMsoM9+\npq7y00HI6kmflnQ8vf0UfhjT7b1puQP1clY/5C1fqetm73kX85Tya0UQDYhV\nqc8saDHh07lMRuQKKsYRMRogl4R1y3ZqlFgtX5NTFEMXsb6tR1LFTKvVlNMX\npBxa\r\n=NbLi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "11223e896a36d7f77af5c33bf09c50c7f560a374", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_12.0.4_1642323057437_0.880310553664047", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "dotenv", "version": "13.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@13.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "d8240508b5d9675ebc46881888b43a36666c630c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-13.0.0.tgz", "fileCount": 10, "integrity": "sha512-eVAD2DMFP/5uE1NIOu9nxzjvmDDPiRqEP3nSHS+Vvs/S8fk4m1AX4eh1QIs95N2wBc8mvXiOQKZGtGywlIVlng==", "signatures": [{"sig": "MEUCICKmyd8JQ9mt7ole1qUjjJLoQOFb/isLMaU1Eu1XKK4wAiEAwaDumH82X8E9fowKBUWQ3bP6iTt/z4k/wQr0DlEiBXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5KD0CRA9TVsSAnZWagAANkMP/jzTo8drrWG7AEikcIXp\nxBR+vzbHQ2KEYZ9xYmBF2+0hbCO1X6w+dcQszIxc9ryZyXk7x3D9UYPnz8B1\nyBg3JRc5ytr0S52AB2kNaT4LGXvXKMpRciSc/GD9MWO+LhONvMkA8jmFJgIl\nJSgzwBBfZupdErXVRqkRMuYW1fcrTRfZgztTyYq3xTNGOfG42RMFxRugoQYv\n+CCXSjitGPPUYB3pycokJxRkIizGwBDNhwVU+19hjRzuOmVlwC3SQGMXOJgn\nRy84Go+V5Da3o3xrEfQCJfm2S0RBB/QHiM6jvl17oFMnLwhuIK2oiLy8QHVx\nHao0SKp1ook4kkhA+3sdsbMeP8Yyp05Bak50Di52k847/oHJ6BGuh2/bSiwU\nBmVEbP7wLFx8YeQKEOmi9rHG2dn1OzVia5FllD4f/jaF0JElGtm2DbX1Bxhw\nfI0FadYFwmGoH3KRArCk1c7mQNGd4FC6u/LwDgtxjGKBECGPpUE0pdvvnEEP\nMdZbCfMaOf0wni8l6XLJukL2G2vpm3zJq4OmrnvF6LImGBZL+Y1TWvVtQe3v\nQ5pICdnyxsuhtItdY7vYhUp3gkk24j6K+GYAAscJWRPlG57p7KIxEauP+l9k\nBmFvcvoqIau4x6TKUlkccweBtmsMfKurkWCB0thf8iYBnK19/5Ua0xw8u4OC\nRr0d\r\n=Dt2I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "19e4f2cb074ef297969f06b659eb1fc5ccf06e47", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_13.0.0_1642373364816_0.3254797158560532", "host": "s3://npm-registry-packages"}}, "13.0.1": {"name": "dotenv", "version": "13.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@13.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "cf43980abe07accfa5c3ee321fc7ddc551975e96", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-13.0.1.tgz", "fileCount": 10, "integrity": "sha512-u3KAkK+VHk01+D7V6SFtSJl2JScX1Yi4anKsKXS4oT8s8LnL5xgJe7XFAZ1bSsOfAmxU54OwOuhaLv3v70oXgw==", "signatures": [{"sig": "MEUCIQCQBcGnCOdG4uLzm9XEif7quU1ZLkaZ5+fdZF9F0xy1ggIgfqJg9GkuUwZQ83l+CXmsvxJRhfbJ0Kx/qF3r1dQS0v0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5K/6CRA9TVsSAnZWagAAhioP/i/yyunP+vkpnuxwGU2G\n6PwA+teFwrNlco17V0G5QmrSFwzFmIHraLJwhdLnFE77fu9OjX3Imy13//DO\n+ev1udvt9FykpS0LHPjpmSXDYlmZakIe0U3WzIwzwruAXB0o+uqJU++qbCci\ni4VKBTrBVwxNWfdp3PKX9aX7aCcGkky/9gsmgj7OmEYUJNIUorNuopqG8nJ7\nYtc6hw3n9taFCoJS8HS1MslkKjNqIoRjsPFd0Ima+x7mSgvO2+m8Q5/alNHB\ngpXkSIG28g7Nt1XOSI8+FHEBrkea7ZLLKvvVd4a223iGPhhveaB+PDyqauhT\n0lCdemvGW1gsmpp/53FaeRcPViHj873cUAWgTfoxPaRo2UfMDTUgI07UEoWU\nmHS9v8wVdE/Us7lPVmKP4b3PuS5wIgU7ARHkCtnYRJ6B8i6TLq0I88vltNZE\n7xrqoAR56s5OFwckxOU4hcp4w8n/LigrwGnPbzh9XkJIEGpwcQkDZ3skcn3k\nER1wa6BqdnRmQ/gktiep8rTMmO9QiQcLfsr+6pWvBLfb5dAsrEgNGnIhmoQ8\nncdP6FG1pseYKe+tV5TYTIFB8ct+QJ5bhRRIgakPxmt33e8b4cTuGEziQ1VB\nJ6HnD5pbpoSeXvJ9AyrUqoqvHbc7WTzqBPPs7/KOSDUiy2towOTrOgC54+sD\nNs1x\r\n=qwGy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "d1a1d0791c4ca515bdc65449c44066e01b3a45b8", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_13.0.1_1642377209865_0.3999715487775155", "host": "s3://npm-registry-packages"}}, "14.0.0": {"name": "dotenv", "version": "14.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a993719c572eb39066922fa2031f0bc40ad88311", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.0.0.tgz", "fileCount": 10, "integrity": "sha512-lO8c74ruYOG0hCK6fWT7t/PVxqcLKhfOqTahXZQN2UCyfN+ZTJmu48wRUVpkBAXsUGjPOG+ndTh8rjZxyf2xqw==", "signatures": [{"sig": "MEYCIQCrkdU+uxbvdY+xOou+MtjGBZZEMVXe5cYME73lsVF6QAIhAPHWHTBljZRFagCL7x8ZW4wN4fYT3DfbVzsesJCsV2hZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5OfLCRA9TVsSAnZWagAAOCAP/iuqoExzlle1LMsSA3Aa\nj3lqtwC5RBPBhiaEI5FdSMSzuweWhuqVxMo7crFu7QG3+tFKlpOGe+D2lFjH\nos994YfJbtdkFr62v5wt09sx1JYtYiqcSq6SoXMOlmrIhbPAaPv2cl28X+rH\nUdqrAlyr6clmdwYhAdx9ZmcIbsUy8wlkVainrNfhslzAKs8lLCH2r2XJb9fz\njuSArmhrcmnECJLr+DpeVU94xGUpF8Tz1P77hS9OaeGGIijzSLP04LuXt7JD\nIihczTbUHfMFhRZNgJr4EyfI38Bz+NXCTTryJa7s3ZOoEhCXkjrlhJmiw+dK\nxvV9D1FKqXOTo7Ay8cTkVaGhcqqXE/CAm4MRpQRHbTPgaiUojbVlCkPUdeji\nnTyCZqcOuM7LHuLtPYc5HMk3q0AijefF7vyvZYdFQVz+KIroCpWpz2qj0+2o\nM6QezXfqxSPNKhUn0T65EKLkS1ieDwAyIwtLMMUzmVtw0hy93ZXT/Y2vcSVz\nEHhan1z/wKZG464LCrDqaPRQROvb2jMqMLlxFEm4BvEZ0QcMw4MKpyO4Qblq\nkoI1ylXX1N46P4Pf39clxd/Qxa1kd2tOPe9dCVK5C4wkD13YT8Lqqi/N/blk\nouXO/UHh6KL6wgv3UOLPgjuX0sOpqmlY0DufdphTBgEMawEs0vLSZUB5DtBU\nRPB9\r\n=+zc/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "42eed1f52777b55dde6d241e017839a0083f8f2b", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.0.0_1642391499711_0.07763931837757032", "host": "s3://npm-registry-packages"}}, "14.0.1": {"name": "dotenv", "version": "14.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "130e75a16df47de88becdf6d47527df76f864d33", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.0.1.tgz", "fileCount": 10, "integrity": "sha512-CuBFi54IgZtRqSls21+DLbQ1YZGEx80DerXbnj+wCGZ+UvxQdfXvWJliB3n3yaeaNAQQs2l/53iZwclTHtL3uA==", "signatures": [{"sig": "MEUCIQD6DVlJqHgudgK6+XQUo6hRL96e9HdX5iHs0NSibUIIZgIgf5v3vJrrdGSFW/4H9lk6lEIqnxTj8uvxZ28E1GmE8YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5PWhCRA9TVsSAnZWagAAgkkP/i6m6mYDp0Gp095CXdhB\nT/6ubhrQIaZZVblTyP0PlpwbRk2EURmBbNDbIfZJjhjNmTYFm1/Hzrre11xw\nzghOAF3vytDYcCME+OSaM5KvFSNEI47QRgWolo9+7SBhcv+aWBYKeFNwgrbg\nSrQzciUb/tohTPNXmfq/lH7Q04hDWRz8eVIWj31MgvMzWgXO7WXquqD0AGKt\nt6YA+VhyVqRlT9CWLwnIxvrU10VquDJKsOaqKEx6P/qmWggFAGiuj2tpVPMY\nhuTIyDciaVMna83NMyFOh7ZiSwVvwE5V/YU0VsHUmzhp6zxagjUwbR+vrmt4\n4z79e5aAidU0rER6Vxp9rFqUfzCxgxDQyXB77WHxeCLmMILYcFElN0VSukYs\n4p0tVd/PlCXDveaGUao+V/C1ZsnmsKO1NNFH4i0wOR2RKn1qd48sp1aikAty\nvCizM/DaVVCy82tsfej+IxNHU7SAz+kXLcxB8TbwKQyg3KSYCl/8uP/0cTTK\nhYtwk5ZiQRtWTcOpBz0A+u29O6vdthQshyPsdtRFieOMXWZsmXNJvFXmLkvm\n9gSNjHX5NkPfUmWEbQXl9kmMI6Kn2NB+BPKCB7zt0a9Pp81vk02LyO0GAjay\nFu4RsefY8vC2AHDKENnwiT5oYOjr4SRvwjesodAZlLIT/LYdIrq7UgCaJ+Vt\nTeNv\r\n=JILu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "2c6754ef2dc17b4eb45a8634d96cc23cb24ed41d", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.0.1_1642395041097_0.8602878823046818", "host": "s3://npm-registry-packages"}}, "14.1.0": {"name": "dotenv", "version": "14.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.1.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "66e9c0c448501b006f4dd9f08080c9011c7a5e6c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.1.0.tgz", "fileCount": 10, "integrity": "sha512-h8V+Yfa8m0YSjf3Rgbno51cxWldb4PEixIJVL55VmW7uAfeLQKiaPrEUiBps+ARK9MeqjJgTf269OMmu6lOODQ==", "signatures": [{"sig": "MEYCIQDyu+Hm6nqOwloC6jN4sV5Oq0pnPXSb53XiGZ4qV0/GygIhAIxmccd+dzmp+zeDmAza/n+Ntg+nUaYSYlQUUTdp6JNe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5QSKCRA9TVsSAnZWagAAOn8P/RyjStMlTN2HZyE+w1qm\nXT2JUKv0MFy8CCKYWdxgnHiRiQrWW1s5yzoLlXWBJQh2fbb+tUWT6LD1BLeK\nV6GxdOAnOSF3Lb+sVtLSYXxlW1i6B7tvFAvPwMymhqX4iE8ZesMlhAteGM0O\n99roBHGKC8cjyO/x4b48IIUdvrIEpZnAt3EmrTtdEj9dV+Tx6J4Fyfk6vIjV\n0cZFrZaunrJ84KPGcPfnvUesDNVbcME5jGiJqwz2uq1tFWOm4gI5/PvDyPv9\ng7LULMTQ0+1yz2hreeeeUY/8zNKd6i6GE7CuLZASR4n2yvdVXMuEK14+nZJp\ne7G3+K013+SDCaDskAO++0xn66zHeBBKOU6sifN2o/Yl14TT5gAXKJSD1yb8\nSLIqTFhbRtyM7ShwKPk60QojuG4DuU1Jdmbx2HOw1uHfbKkfWu5VgO4wRmV1\nSUAv38emlfuFJ/9NNgC/oJMvKSXwFEmPivDISGTIIhoRMS8koXYLtM3tSUMM\nVyPuOr3mWjWI67Sdlag/JhGnAn917qC7JIAILqil133p0J71ERjv33Mgz8Pd\no1VMExB9RLXRfMQucLSrsvFMZKL1gmRYc/6Ys7oBgvDR9Y19+Xkm+dnnWvpy\nw3GqmQ/EWkNPuMF0ldl+xjyF0H8jkW37wPlEWMbq126JVwhVMXxIqio95ZPJ\nsVdn\r\n=KNWC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "9509ec8528accffecc45f7caad2d1b97d892d299", "scripts": {"lint": "standard", "test": "tap tests/*.js --100", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.11.0", "sinon": "^7.5.0", "decache": "^4.5.1", "dtslint": "^3.4.2", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.8", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.1.0_1642398857862_0.898722510092588", "host": "s3://npm-registry-packages"}}, "14.1.1": {"name": "dotenv", "version": "14.1.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.1.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "54d2cc35c938743659a5f8bb7da5568bbe094c3c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.1.1.tgz", "fileCount": 10, "integrity": "sha512-g7ptu0p9gCDIWTLY62fTYT+PasFLNj9FRGg8Wmjr0v/dOL2LEe8guNjpkY59OX91fwIFU7qth4RO89pDSTTG2g==", "signatures": [{"sig": "MEUCIQCwqAe6UY/TR8U5bS2br3FFNIevv2TuWmi2jdzRoB5hJgIgICBJEZquJfyp63Q7LjZfHgL2xLM6lxfqq98mSn7XmFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5dB7CRA9TVsSAnZWagAAiOQP/RYCPtRQ+T04+s1r2ZkW\n8UBtrBVqbx7FQhL3JdKuCW7uo5/Lg5/tQxZQnba+R+IUqYVl7E4lcE/+So+f\n+lZlYn3jBbri5lFrMr/H1uNGYZAgt4POBfrzZVU3yJH0r/7CecFgNzsdNUO4\nuN8wHHaEv0tLEwHmAfvpbtZYndfpl2wXEs/UzJa0vPm+e8y1gvzeDQCHXb0r\nimhNf5ToqV/RrZf517KCo7seqe5rjmmdncIO+bHBx3IrslrHujURxoaC2+vV\nLWQx7LqMH5VJqe9Ypv6bAE4qVnGD5BHjH4GvWidgTdwZJ0BJz+E4ZqtUbd8p\nUQsVPd8g849yBYQn0+mJaALwJwXzpIHpH+SK654HWhQGRaESgNCAILgLgK3S\nypzAoLDf2lQTy5e101HbVdDC7Z+F42H4VoLuOBNoQESmrtHir6zlw1YdhjT7\nW5SMnmw5Pj1Gl7Fvo/PVMskGqto7aLBApTjtt8zAUVtIyd5UJZS1T2i7URKF\nRIyPnLOoIv745B7CFws+MWLU/ipu0gpFqVi+TBdD9fF2aQKBHr7YwqeSWn+Q\nt92UQ/XZq5+K/BadmGwlPP5HoZztrd4u+9bVN+9b3yokA0lEC/MF1lyscFJJ\nxRqzTB2hGDjiz2lt8i3cYoV4DEZKBsf1PD/odMonmQc2w2pdjcBXhJnQWTrE\nqPPS\r\n=/2p1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "18b6e1ccdcac677f61fe7cdf10f8438e72470b37", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.1.1_1642451067354_0.059101089549951524", "host": "s3://npm-registry-packages"}}, "14.2.0": {"name": "dotenv", "version": "14.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.2.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "7e77fd5dd6cff5942c4496e1acf2d0f37a9e67aa", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.2.0.tgz", "fileCount": 10, "integrity": "sha512-05POuPJyPpO6jqzTNweQFfAyMSD4qa4lvsMOWyTRTdpHKy6nnnN+IYWaXF+lHivhBH/ufDKlR4IWCAN3oPnHuw==", "signatures": [{"sig": "MEYCIQCuA25PmLb438rApTvSsPxBtdATWGJ+XEfGMDJ5+O6tsQIhAP5NTnoB9UkjvjrfGWwT+Axy+8E/hDB99g01jp85DO1+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5dvgCRA9TVsSAnZWagAAbMEP/A3DcAqBQo52cyyd1TUi\n91vILebgw8FISQFO8n5cl1NCRyCgpNtDVz1suj3fGQ65n7O8u/jJgktv6EdN\nGsgwFFr1nfcl944Hz+OFqODaLjmVZK+fLT7ue4/gLzYmMS2+rYTcVH/KrUQ1\nnR1acVQ/UbHLYWoVni9ITjuTb4PCxO+laCgcKp+2vD6yulfF15G3F/s09cA6\nKi0MrFtniWZlEaeREzaUC1PRykbaWIyrwaBn7POy67HwLGuCNqW5EuDXbvAT\ni6wsBPizOxIwM4jIPBgfQN9itokpEYyMLwd64ZswhxWBT7oZ20Btu/I0n+21\nkfde0CoKK5oS6FLqXjlmraAFCQSeJnZzLC8ZZGC3D09ap5nxvY8v43iIvevp\nvfjwuDD9Tcl8RFP8Vn9l/b05pkOe5MTjzno4mcCK/hqKVN1dz6fJeDCzjJMS\nRCQqqSF4/czBIvZcFc6M2TM9bUmWKuWQ4oELdsgKomwerj1QR+GYayfEU8t1\nu9cyVaNZF8wes/jiqHfMgnAXZ14d+3Et6ezepXZz+vN2Rq6JofjpS3+JJvfB\nKQsUBJjsxWqRyea0eYv3E9NtdklIRubabqURy7x16HNG8BYalTFUsKxooWrF\nCGe+FtjPhJ0X4p0dsoRf4QL4xD50vTeOCd/DoYRzVnPtQtYo0oKUF8zSqvRz\npBPd\r\n=fbyK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "44281f435eeb7e2aded69178ae7a9a72e85f50f3", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.2.0_1642453984373_0.5999307757881047", "host": "s3://npm-registry-packages"}}, "14.3.0": {"name": "dotenv", "version": "14.3.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.3.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "40f537fe90e229d35361c66cf432903e0db49001", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.3.0.tgz", "fileCount": 10, "integrity": "sha512-PCTcOQSXVo9FI1dB7AichJXMEvmiGCq0gnCpjfDUc8505uR+2MeLXWe+Ue4PN5UXa2isHSa78sr7L59fk+2mnQ==", "signatures": [{"sig": "MEQCIAtrrkIqEhPspz98uUAWSeZW4Lr+F3XDa3IwocMceVObAiAtvUjSwUSfUgRqP+G2oKh3EkeBWAHdX/P7PuwOM3tDZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xWICRA9TVsSAnZWagAA+ZYP/0NGpUGC0ze6fNblx/f2\nIB7DjSvZy6Tur6t87Wz2sMzUGZDBNsB28Oqdocbu+oRBxTLFbZcC8ntuPBN5\nbz9rKyphNAnR2lFHsXaTP05BhcbQZ1XyB4kmqEIYF3kTuast88lLJWMaQ0wU\nx66RnKtUKqMIeRs0uvoe/CW7XpjkwurKU041v6DRjR5/ps06Om5skLqSvnjv\ntMG2jEYBqI5FIw51+C6hp/jMdv2OlGimpatPqKWPgSh6w06y1sWQx4s1cbjy\ndY3X47I3MFxNqbV9SBkUxq4cUkAxMBbqS1rA3Bb9VgftgPs9Hs93pcIi/WC3\nsgUCiP93qSz8iu5aL5EyUAYwX5PMdf0sJ1u/qjwec/XMbtmFaGaIeC2DEsk5\n+c9BPxjGsWtJaG20Rkwhtpfj9yzP1VSXHquDprRPDmHIo2qbrfqVviovn3W6\nHUQ+FL6F7/QmkWT3sFjHnDfEwrd4eAtSVMYWcnHM5dETQ/AwqxLhMFeDny87\nYIh5dLy9dwLd7sdGvwHExpkArhRpxyaGd6o8T6Dv3lPj+OJqroYs1B1pSCJE\nVZwcBluRmB1/24l+FxlVD+vUTqT5Ejz9CRZWs0W0w3VNeWw4gEhsozEuB7ll\nL3DcJ3uMeB5uk7dNcApMXd8y5l/kt42Bkqx7sbxhzrmIPV9EWHT2Fmiw3X6X\nYnmE\r\n=e1HI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "a1bf6c1371c8c9c5ab51f9ecb08ebfae46007910", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.3.0_1643058568571_0.8777021103927893", "host": "s3://npm-registry-packages"}}, "14.3.1": {"name": "dotenv", "version": "14.3.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.3.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "c82cfa57792b6081eb82de19fabc59d0fe2ad4d2", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.3.1.tgz", "fileCount": 10, "integrity": "sha512-qMpNS0qiX1sFnBTn9zcXxL+IPcnb3WJni85/R60vSAE4V0TBWj9pyrq0dOoK87B4MdYkAUWU8WL4W/4OzYn9LQ==", "signatures": [{"sig": "MEUCIQDGgcm9cZYrh1xptGdRJX2WJaxfPp4PzFem9YRvEqE6rgIgN9iEx0+aifnyqdLf348pff38RhHtUjbECR0qvmEbwEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8G/BCRA9TVsSAnZWagAAvR0P/Aj4sbLb66gclbMIqfdX\nUfIX5/oLxmjgSkLGDbKBHQLE+ujls4j5qMGlttKK+ip5m4wODAXC4p1KxwbE\nQXGQRlVeJo+cpT8Q8ZNqj1oFnLMqplBoYk2i78SG9wMCzI9Nbkyr19zMT841\nBikDQ3NP+rtWYtEgSjnxvE3+Es8k+Fv3Eby72MiK+wo44MtBwfw0PPmjBoOY\niJgXyOVPQn5k5Cy6Lui3DwTpH2tickHHC+SC+qC3IiuEnZ+vOUE7yZFQWDP0\nj5nZabg1B9z00h7UTk+EwMlfQqQcnfEjobtNXNdUmwUIbMeihiFvpGDZD6rT\n6w2L80j+pKREfNldDRsFT3kWK46tJ0XffC3D7HhRBtcgohKZwWXER4T3BkuQ\nA1x/wgYjpKX1DSYcbzcTZWd3sSV5UCCehRSAG+sGZGP519nq+QMJa7+sW28U\n2YUmz5+5+ZnAw3nEyYIYxQMxOBuMjtvsB0KgsWQBGbjS8WPPulUQdfmQbmlP\nj+VbaI/cGwLpHsYTqEqzk7Erncp9bvqb1OfJ0WaVKZYtFarm2OI+sAK/8/zG\nzTMUtJiggXbEhOgBTSqIlGHQeS+C/e3/Y776Okec19QhVPLfPpuAIup4OX0i\n50Of6IheGKhvEg0mrZyDVHo8eB7zJYiwUETAm8AuAHqk5ey7vE+CfPYROKnh\nqGS2\r\n=Hvb/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "52e25e00b2dbd1bfad31089c488bac91e4e07292", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.3.1_1643147201082_0.5446362718118509", "host": "s3://npm-registry-packages"}}, "14.3.2": {"name": "dotenv", "version": "14.3.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@14.3.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "7c30b3a5f777c79a3429cb2db358eef6751e8369", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-14.3.2.tgz", "fileCount": 10, "integrity": "sha512-vwEppIphpFdvaMCaHfCEv9IgwcxMljMw2TnAQBB4VWPvzXQLTb82jwmdOKzlEVUL3gNFT4l4TPKO+Bn+sqcrVQ==", "signatures": [{"sig": "MEQCIA13SUFJ+CTT7wW89iOx6enQBdYV6WrpMreAb+I339fmAiAMlGikMAS4pI/0MMKHP5jtF6ijOI2u6+C6B9vseNhZsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8Hd5CRA9TVsSAnZWagAAQPgP/i0xDyDUlQuRoIhy2yXX\n4GUsBO07Ob1gZvc5qlTWy0J59G+lBA/r0R6eFi3hQWTByfx8NYT6zdkrqU0j\n3ubBawG+PeYTm9bYg63T0s6BmL7VN8LWT90WiGD6qz9en1jKlEIMuYiO6BjM\n7HI7lyZv2qxLbevJbwrUKksexZ6qpp9Vr8r5JSqsjH2+6M0+4eEhnNogImnS\njvvuU6VVO0IIwoNoCsBeSnkXMvQfISD7+tuyXybXyw6PdwHvspTWG8OgFQgQ\n0u1fry70/f9Vu8J6tj0C9o43ZdihNmoX3fZFMOxdMIhXnkzQT4sHFhZenzlj\nPfdHpJct3U2GUEGrJZ3pGVL+EkPclVZkiF4VOl10gBuwCOFN0n8mW210By4i\n2dpxstTaK5z1Nmw2y6uoZ4zcv3+2tfIsyDVsuMw7HZ3qhSe7B+7qtODuIhLy\n43DwR1W1jLp8gQVrmQpxVwhyCUj9ZdSqxjkWW/DoWRuTpDVy1LzY9FBK+aFm\nV7TAKGixrHGPP+Y0st1KBeoRO61GYbPcaGhDM60epO8Vs5cW0HKQFJ5a2gZ2\n+RFRhGmRO89Yc0DuH3Pdqm6GMNEm3dZ60AtxxhlDNhSgZQP/yiFSLXAANpwu\nGROpnLWFwCkSokyx+IacTBzy+GYpdmksjs112DxNM8S4FRwKZ4JR21R1uVrs\nZuI6\r\n=noqf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "70ecc790e2a67daba860e93f1aff06d9fc97ba01", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_14.3.2_1643149177808_0.1371153780961396", "host": "s3://npm-registry-packages"}}, "15.0.0": {"name": "dotenv", "version": "15.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@15.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "2a585639429bf2c2c62f387f0fffde9c75aafde0", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-15.0.0.tgz", "fileCount": 10, "integrity": "sha512-/l1sXXm79ry34KwwS0y4oVZjB468iw/6u9g1W26dtexKcIJAnVL2pMF+hxQwzZ7LutxOwEgtym9eIxvX33CMKg==", "signatures": [{"sig": "MEYCIQC5IO+4NvsyBidF0rOJpMgmI+edCoMfPdkkaAKhe7hXMQIhAIt7YUbGvop/K9wM/AXWGNYlWcbXYMUYhlbucsWdCVrI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh925ICRA9TVsSAnZWagAA0GkP/0f8fSew7DIsac2g7xmw\njDW2LPb6DS39dBlNbXkPtrV8atwJbsQfXei7+UllHOO2MtkFakkgRiPu84+l\nmU/mNZfMwqgIjbiatrKg68zdSC3HVkfAl85pbYzBAc0fjKN2Fi1kNnd74nW1\n5p4HawzrylpFBv0HTr2vuOiVcIrhKxVIlfN9+MyG6mClaRJxEsah7UWhb2en\nWmQWFVZoHUjljHg/aI2TdeQ6AL282LkDK3qAJAF2b8Fqsm9rTOw9/FofGkSg\nwJuyEjARPyB0q/C32g9IaWBG64M+fDS05VBn4GDB0q5xhN32u+8gtvk4cmgE\niTWrHWePIAqwPvEO4tYhcMgw7Ma/lYInQHWzRvVuzCua3sxvsobw4Tb0Dlbe\nBgJ+LfAE8GU5M6iSIlKsP/J649JmWWCf7thodsMeKYnDqlN1gCmTvJ9XKA4T\n19LEMzRQyrtw3GnAlYn2yA69b+cP3VOisKqYk4wccMEyvc7BzvgEfvMXts7R\nWzUUl2HJ9uU6+LE3EjFYLro3B+11gO4wYSTllELNcqS0pkYtps4xdqoPxAo5\nem3VwM4mLwqC9gglxxe/FX7lTqAYUaSxH/q1k6+R+oNC3Jr1lege67tj/ZEO\nR0Pv6r5h0XtZU/GxvOGSakY7DRPS7efWFSZDVkYNrrmW18TRvXgiwTqp9yKa\n3WS3\r\n=cDiH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "4b7a13007682296f2d51c0ca04e10325c59f0df0", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_15.0.0_1643605576237_0.007941837828691067", "host": "s3://npm-registry-packages"}}, "15.0.1": {"name": "dotenv", "version": "15.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@15.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "bf96a46782923e17a64b5edb35eb50d2292efd65", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-15.0.1.tgz", "fileCount": 10, "integrity": "sha512-4OnbwRfzR+xQThp7uq1xpUS9fmgZ//njexOtPjPSbK3yHGrSHSJnaJRsXderSSm2elfvVj+Y5awDC0I8Oy8rkA==", "signatures": [{"sig": "MEYCIQCGwcAyWf/zBywHwWeKIZD4boU14qVSC5h0aOeaPnfZtgIhALHPhZWMervyO+TSVHR+XSiOIdFgGlbbxrzJHVTdgCRl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+uTfCRA9TVsSAnZWagAAzEQP/Ah1WJgT04VnBU2/Bsop\nQ4PZ2+p20aDut9q/AmoiZBWz5pNibLLsEzJGk/zXzxKCFWMi3rSe3dX4tfZl\nHsXibc92Rx0Gv3gfGJ51ZcNMYhqdZTRBOhL0XLRIS/I8azVri8LvhaeG4cji\nMBRb8Mv5HTjIFV2cqmrn8iZf+ds6BiCliG/B8Yee8AhvsNy4mJlHX3Ae1i+V\naDZB70GanTdYhWHmECgu0raqaIIS6+bkxnFI751Au/FzZ59pxKx1X7KCYRYe\nB8okE+ZxJJiwlVRNZZxAEajbmol/2xaSwvRjue6GLFuySH224KaOxeui95Aq\nyxYQM6mhRaFbIipQxeh97Y9qaN8PikVe2n2v+xyQ/WSVXXcT4f+6IHo2qUqm\nVwtyWv64vrY+WGDycw+dvs6t2RF0fp3m8zo4maGUCdi8pX8PBDmnASrvsG9r\nbEa5HTEIKNZITwtvg4cnGm/y/wYRDDrWAHHGcINRJMwjzA1kfovq5jvZym5F\nGDRFOhNtYiplyEwwQFIz0B9h3U/YIZi5xZfAOdZw/479c7L2Zi3fdB6/eHKd\nWP2z4aB3XDB7QeHCc9O2n1GwaYR39Zsb1GyKvf+wRxchDaMpv8WMNFo7j44W\nDtfB7fzO0lmcnqH57+uoYTSrF2dgZ9jeSqbjR/jVSAtsQR3bnT5QHr/3j3Kq\nsyyL\r\n=kqHK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "d6184e53364ac3f1a68d43be5db4a1dec73033e1", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_15.0.1_1643832543511_0.11644209283164719", "host": "s3://npm-registry-packages"}}, "16.0.0": {"name": "dotenv", "version": "16.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "c619001253be89ebb638d027b609c75c26e47411", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.0.tgz", "fileCount": 10, "integrity": "sha512-qD9WU0MPM4SWLPJy/r2Be+2WgQj8plChsyrCNQzW/0WjvcJQiKQJ9mH3ZgB3fxbUUxgc/11ZJ0Fi5KiimWGz2Q==", "signatures": [{"sig": "MEUCIQDgRPnzrPEys0E4UIKbaddkvGytu0zuuSB3PT1xNqplgAIgC40a85Mpjn0Xiq150S7/0FmsCMDkGCC6Rwtin/kV410=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+vbvCRA9TVsSAnZWagAA168P/2EP/iY6gxwfX23BrDln\nNLgCzrDRDG27Dc2YH8/JM617XSEsV7Svk3FW8N3z0NUG7KGsTrXIjDSei9di\nwJ6a9SYAJv4SjyTpdvFCinsEz1XiOShEI+RCeyTEo+j7btxGvPEBIGOC5/SX\nZftiDucD9X/nmDXfCjHXdvPRykwq/Bqtcor5RQmFFdZWJOoeMqXsjE8y5s09\n/Bu2+nhfHenQwIdQMIMtLXgZHmj5RzP7dXVcu6ELX5mEVWTR8ivRpAISxBFm\npmcBxQfWNOoXTU+U4ZBVADic699uTWLwSFL2SCGW2+F5B38T6ssZ5jPXa6qq\nTGu7+14jr4A7KCTzT7vWPCb7g7VryRuyeC8LGe0Kddbp8rAlQM2o3yBztHLd\n670shjvnGOoh3WnQnGpT8Kl8ejtyJC5Lbbghkz7tfHb+GHwfVLHGBh2AAgPb\nFHGaxqba+uWq4/kRxw7qaoEpE0TeQcktjfaRDr8e1Ze6ALU4yhjOiF01gWAM\nuUCyqme/hXGi0Ub1cc5UBA2kYKFi3jWRCF/WA71uF/9HdeWAl3KNSgLHtC9B\ngwqDauL8HxoQSFhVqmbVvz3kNdhg5DMDv2tci3t/67+SNw10qMraObaDfogr\nIjp6TeXXN6n7iAjGP+WP9lt20FjM8glruZzjZ7Rq+mEP8QySg5b9fXDO4T3/\nI9Vx\r\n=5YSX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "c20ee46a01da4e3169ebcee863d1b29a9ce1f0ae", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.0.0_1643837166959_0.9353780123214481", "host": "s3://npm-registry-packages"}}, "16.0.1": {"name": "dotenv", "version": "16.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "8f8f9d94876c35dac989876a5d3a82a267fdce1d", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.1.tgz", "fileCount": 10, "integrity": "sha512-1K6hR6wtk2FviQ4kEiSjFiH5rpzEVi8WW0x96aztHVMhEspNpc4DVOUTEHtEva5VThQ8IaBX1Pe4gSzpVVUsKQ==", "signatures": [{"sig": "MEUCIAPr586/DybZGE58aY4oA86/LiaRdFJrTFZWVXQKM1oDAiEA5rRaqAAoXvN1cttUhWXibitFUpyI3RWvJtPPlNtO7bU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJierjtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqihAAlEpepHs8reBqjYHGp4ZLKJ0iMXlpF+k6k7KvP222lCcyW6Zd\r\niyi8NcFK/4H3yJPviYbZbM7zzigtE3cBS10k6s31RzDB8V1EZQQZelEJ18V/\r\nsAVE2TxATIG5oiq1qmY1kdR4/+7rwAXbW6ExO2CZMdbbQ7O86BggEncmFQqu\r\nJMTN2fR7jSWTL3ypIMdUexu9v64FoZYvTQu68DWZq2zNQ5jJbtcHMHqAjfUa\r\n0kF8W9Nq9Zuge+vijmuvpMwb28CpS2j3fccGTP/7v21/YGkIQhL9nre1R4bb\r\nY8qyESPQparjZIQhLniiXZJsfo9Xz/k5uKVv7r0S7s54yP+Oo6ewdvz50cqD\r\nEwdbpwv5zwDsw5RjQTZxVpB9SMSlEeHVJ1eK4fDi5MTZCWZZIA5TPF2iSrRE\r\n9g+l5Xj1iFTVppoRA6MZqiaIsJm2yeBw9KNrwV6C9hkgCsGwUyfk2WDzaJ3A\r\ncHCMADEGqm/PjScy67nzwk6/HDlbdT/Jc4QlwvgKhzDb6FIiFrXqXPUxZuw1\r\nW74SlF6T53oNC+lQBVXefGkkmaYNxl4ajYOBgmfbJfBPwgwbnPSOiBsYXF8w\r\nXEh9ChTnvkbeVeexfC0fwq8XAIJdLCi/ajRESfxatPQu5zAsFOCI3snfiv7r\r\nlvM5kj17hYKRTzs63RWT2xibbnam5TRap9A=\r\n=GVD7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json"}, "gitHead": "b016108212a177af7bdfa971ef005c69c266d0cf", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "tar": "^6.1.11", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.0.1_1652209900787_0.6170912550909378", "host": "s3://npm-registry-packages"}}, "16.0.2": {"name": "dotenv", "version": "16.0.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.0.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0b0f8652c016a3858ef795024508cddc4bffc5bf", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.2.tgz", "fileCount": 10, "integrity": "sha512-JvpYKUmzQhYoIFgK2MOnF3bciIZoItIIoryihy0rIA+H4Jy0FmgyKYAHCTN98P5ybGSJcIFbh6QKeJdtZd1qhA==", "signatures": [{"sig": "MEUCIFwiJ2dO9ceHs8dYw1ows0CUAO7ZlbyCHSMPc0FSL01iAiEAvAXTGZLrQFQX7FnVzy8DJx3dAni1/8xuCPB2M16IC9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDl8jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/fw/9EiyuqaGEZfnlHNiim090xsQhkBSYuZqx8dUWL80A4LW3o3PE\r\n4tyXuvdCM9Z29Um6UaoYMyADBPPxgffuUcdTwm7nRpAzHo8H1ngAioUmFzeO\r\nKlIrOdV0fmi42ZoAHAnT4cq8042lQiMHEeIA6UM+QauG3Z8sTqnF3CjqTQ3Q\r\n6rlfosLexIH9NL5c4cy2/KsJPzWaIKcx4Nh2JhUnovMQlo28VSafJ3eTxlyK\r\nQCsAVQxWO/eveFEwTp+QW0Q9VVhhuDnaaig6lOheWqvQh7cTAd3WBLjJNZVj\r\nOm3U9xcLMzLmyKOdfJeLsVcip8yXveQufnLRoKoc9IlHtoXUXinPRqA9PHNd\r\n8hhmleVNe3MkBSnCWaXhvvmxtofM+SqWYzF/7AOZHVm1c97vQsTzOUqSOwrk\r\nHg/wEsBh/rtsdFjoOo3uTdy3IXg6lB8C19ljc4ZZCH3Qb2UVWtLtt3qtw88k\r\n8j1MOhePIQE4KL5k/GeKWMqzL0Adi1XU1mCuzq6vFOc4Gmq6DmpBXrHtp+kl\r\nNwlG8H3IrzJ1cDLrlXZ9ZAZw0IPm3jVh7VWp2GNFQCJIFRY5gFCqRZR2hjO7\r\nzqNaKdjyg5lxVS4CVPXsebIpc0iPvFyQYTVCuJG7E0T4p3rEWIPBFFnstdPz\r\nYJTXrS/As/j5XPpgKLVgxYJBrJCBj3v3f7A=\r\n=mdOu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "gitHead": "0757a1b3894d2237d6139394d2948434ae32e9c8", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "tar": "^6.1.11", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.0.2_1661886242993_0.5679641398237041", "host": "s3://npm-registry-packages"}}, "16.0.3": {"name": "dotenv", "version": "16.0.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.0.3", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "115aec42bac5053db3c456db30cc243a5a836a07", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.3.tgz", "fileCount": 10, "integrity": "sha512-7GO6HghkA5fYG9TYnNxi14/7K9f5occMlp3zXAuSxn7CKCxt9xbNWG7yF8hTCSUchlfWSe3uLmlPfigevRItzQ==", "signatures": [{"sig": "MEYCIQCBb/F1viAD5jp4saw3kXiTJVv+QwVQsK2Z2noES7O9iwIhAM6sD8Ym7hQ848uMEck1S8/1vnWUF+IqtbIejbvH/OdW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdWqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV2Q/+IwwI+NJAKIQzmKGTmB68NmYJmU4qr3LYd9liMQi6UnCFE4NE\r\nZ/UYq3f4xkjZ0+H6O5vDmvgM5jBdxVR3pHLR1ONl/iVmtjpuI0q4Q6CRHYSD\r\nIdMcukIfvZhR5CfFgUPcAzM+ETCH6MsYnXAeO2r8PlOm2Phd1ivhyBCxdkam\r\nv8vOSteoeksG4b93d5YdY/66bsQh2m565qdpctYE8lWNMvpcob8fj079SuJW\r\n1/FwIr42CKZKeAdRhDBAcxLm+SJK0unPH9Dk5uTcfbzKLRigrqJ8JwkCiv+m\r\nxEaKH1cckO22WKDLsBg51UsofjAlkd9KHXpvmXMfduMjhBobvXkuMWCxv1oA\r\nDUqJ5kphgoezpJXgPvsfMZN/49pT17JKfhMh1FRFnVxpr3SmecaIUyvwstk3\r\nadUFSth8Vk1VQJeGpbxUmpf0gNUz7z/Rpx8zNeqFvuvPCTlFCqAQvTnZs5zX\r\nS5byoxFfsd9jxUN5712keFERAl/Nc208TXsgGpS97yPDqAVBtNeNBB5/zpmj\r\n72TYl4Xb0JrOXyFZ8cMK9fcuzgnBYxTSki/cDYINUav7vlcTYDyVuCTCQpk6\r\niXt+6h1Sik7kjOGXALTumv6EA8R1Ww8vy5adb29/fYZXQ/vWuxU1ty+nMYdw\r\n9/qjOCmjxTO0K4Eo8Xr37WX794xRKZd0tiY=\r\n=RO5q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "gitHead": "560df1555e7fb5cfe7254942e4dc54a16a3316f3", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "tar": "^6.1.11", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.0.3_1664472490438_0.7370011550535194", "host": "s3://npm-registry-packages"}}, "16.1.0-rc1": {"name": "dotenv", "version": "16.1.0-rc1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.0-rc1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "110fb4a92d0ea040f9bd083a6f969b691ab83abc", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.0-rc1.tgz", "fileCount": 10, "integrity": "sha512-T9ZpMsEro6BE9RMEkiOIcT8LD3H7fQAB4jbZqeLnwNRuKadH+WjuC+NXoyfdajInw13Ooq2Hni+3fp2p/PV3KQ==", "signatures": [{"sig": "MEUCIQDOPmulU5P/yWhqlEW5yohOkKXmvElpcV02Pefzyn1LugIgbjMVOTkDbVZUQ6u6JV89PGU58BXbxjZxLewc8NjsWnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMJiEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraNw//Zy4ahGiSFgqBGOt3RI4XAONoKL+9wc1QQLoKsWRLrZR0Q0vq\r\nEa8m8qWebMp8LNrKhZQffTk5blopaGI0THoku7C7vAmhekxVjNIS9FiuMIaA\r\n+vOAos8kvOXNGIHcywSdHdKi5OldS745q+hNj5vXCf4PkT9ZrtZjD0x0h1rn\r\neokd9HoA1XPQawKX5S6HjkSlidqxtyzB/sXGNErXdN+Hpu2ID2S//dzk3uiI\r\n4oH2Qqmn34YKjq9Py7CssmEuxTMmYttImHjKKK1PzQ9Ss3FAfOWg0QvsmX3C\r\nnSS5UPmF9Pr3Z/JtGae4vdRlS3F2rE44SokObdsydWFCe4nxxQAGgWRubu9Q\r\n/LoDr/iULnH2P3SdZ57zuy+SQ/0PCo7MdOhGYs6Ywoqpego9q6bLM9wrDa9z\r\nntEI4M2daZ0illy43YevysGHG5J7lW3njrsutNL+G3YPFBJjkrxEYKzTvUsc\r\nkujrbPR2AQQU/EEz2MDjGRjkdjYmdzuiFpG1qD2op/A7nRIpx2WrHZmkNLlG\r\nds+y5g4FyvxhtPSZiwBtLEmdfcPNwxWB6eUniLvN0AdDXsm+Nr6PJAzv8U4J\r\n3d0nNQYHPuJkPIJj7+G7RlRxHZv9sAwvCE4wCvksxhB/blnI5KlGJkG347GM\r\nG9sFaQSDRFaf2Aolbv4QCTP5N/B6DsrTr4Y=\r\n=B3pU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/main.js", "types": "lib/main.d.ts", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "gitHead": "968f0caecb3960c981fb4cd3368afaba3273b23b", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "tar": "^6.1.11", "sinon": "^12.0.1", "decache": "^4.6.1", "dtslint": "^3.7.0", "standard": "^16.0.4", "typescript": "^4.5.4", "@types/node": "^17.0.9", "standard-version": "^9.3.2", "standard-markdown": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.0-rc1_1680906372783_0.13401920338685058", "host": "s3://npm-registry-packages"}}, "16.1.0-rc2": {"name": "dotenv", "version": "16.1.0-rc2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.0-rc2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "92e836267278ab178daa41aae85d6bc3e8c608fa", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.0-rc2.tgz", "fileCount": 11, "integrity": "sha512-PWhwWNWCj4kgDpmF4iUguEzdt3SdIK7/boCU+xHCK7nZk6eQncGrh9x4EnVx5Sh5N7XrPOLA4fk+uAu74tScQg==", "signatures": [{"sig": "MEYCIQDS41megBD4N9ES3pgze0DQd6pGnPHCw6uhsepO4h/V3AIhALUBBbySYWAkGl1CeHLOz+ldJKzJKrNJMJNZw2ZjfLsI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64837}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false, "os": false, "path": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "gitHead": "c5a928737e2a417c31142a94218e6c7a947e672d", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.0-rc2_1684645934025_0.43729106878762525", "host": "s3://npm-registry-packages"}}, "16.1.0": {"name": "dotenv", "version": "16.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "beb4232166ea080520a42245b8007227ad84b052", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.0.tgz", "fileCount": 13, "integrity": "sha512-XiwP/4cqatBNLEnKe169vPZCrovUmYngyVA4DgZ3uIVLJfZaBgr4uT0EF2TrEQqgWDDlekGo0muEYme5SR78Ww==", "signatures": [{"sig": "MEQCICv7kGTpj6CkmjoqjdJyheZX4GgS8qNdF2AlMgrq5cggAiBv3xhxUUyqtFUXgtvnA8Wa6aOccnTooSW4wJATs7SkWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65213}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false, "os": false, "path": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "ef4893532314b84bdfaf7b4e85d9604ca997beda", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.0_1685463447010_0.8994805123132723", "host": "s3://npm-registry-packages"}}, "16.1.1": {"name": "dotenv", "version": "16.1.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "9105a6c486ab66076231fbe7cf4ba77131a61d65", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.1.tgz", "fileCount": 13, "integrity": "sha512-UGmzIqXU/4b6Vb3R1Vrfd/4vGgVlB+mO+vEixOdfRhLeppkyW2BMhuK7TL8d0el+q9c4lW9qK2wZYhNLFhXYLA==", "signatures": [{"sig": "MEUCICi2Fh+RCNXkv74Jh4sXNSvyco2xm51/jOZymzxDyJL/AiEA8HDZGodIg55RUdH72wQSvr9QiSmOOjfshxgAuN3+qNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65787}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false, "os": false, "path": false, "crypto": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "66080bdefac6576e59737b9ab64ae3d688ee0eb9", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.1_1685497290428_0.693897434021687", "host": "s3://npm-registry-packages"}}, "16.1.2": {"name": "dotenv", "version": "16.1.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "5c62cefa33c9e034354f5828c06aeedc4acfe7f3", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.2.tgz", "fileCount": 13, "integrity": "sha512-RiNjjDF5yr9NtjqJqmWYA/XZShmt3r8FtJgAaqTiYqb99SqT0+aw5xAwOIvjMyXsH/C9/fLNafFkkZDwRi1KHA==", "signatures": [{"sig": "MEQCID5PkVAW3HuzMgiNAO5EbreFT4YKIXdqHgANTt2tHSzYAiBvBlImAIuzjEqd3kLBWN1pm8uixnTbdwkd53Sk3IPLuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66554}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false, "os": false, "path": false, "crypto": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "3f40e1245d61122341ec9adcc804e46c398b1a00", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.2_1685546894289_0.5086416635918747", "host": "s3://npm-registry-packages"}}, "16.1.3": {"name": "dotenv", "version": "16.1.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.3", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0c67e90d0ddb48d08c570888f709b41844928210", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.3.tgz", "fileCount": 13, "integrity": "sha512-FYssxsmCTtKL72fGBSvb1K9dRz0/VZeWqFme/vSb7r7323x4CRaHu4LvQ5JG3+s6yt2YPbBrkpiEODktfyjI9A==", "signatures": [{"sig": "MEUCIQDodY5yVljLMZ2kKVn4gb9YYSegoZS6vjr9zGrr6554/gIgE4UuQgy1UmZzy5aREdaA4SoYMeWNMOzSER9euXkuu7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67914}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "4d255441cf3e9e7823c2da475b4c2a1b45ac5057", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.3_1685559679581_0.6573506834121585", "host": "s3://npm-registry-packages"}}, "16.1.4": {"name": "dotenv", "version": "16.1.4", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.1.4", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "67ac1a10cd9c25f5ba604e4e08bc77c0ebe0ca8c", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.1.4.tgz", "fileCount": 11, "integrity": "sha512-m55RtE8AsPeJBpOIFKihEmqUcoVncQIwo7x9U8ZwLEZw9ZpXboz2c+rvog+jUaJvVrZ5kBOeYQBX5+8Aa/OZQw==", "signatures": [{"sig": "MEYCIQCA4ksNy6V8z0Ycv2iZhGSh8M0fyWH9Rv838JaSGfFMNgIhAKAx9Ie5iU/Wc9gpBL5FwyP6Qu7WKwIdM2kQTisMZGQb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67593}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "dc1cd6c8cb276b040e196ec76918786152ede43a", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.1.4_1685893824602_0.32753841913134507", "host": "s3://npm-registry-packages"}}, "16.2.0": {"name": "dotenv", "version": "16.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.2.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "d0c7110551d39c407f407d1afdce49c501c8e320", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.2.0.tgz", "fileCount": 11, "integrity": "sha512-jcq2vR1DY1+QA+vH58RIrWLDZOifTGmyQJWzP9arDUbgZcySdzuBb1WvhWZzZtiXgfm+GW2pjBqStqlfpzq7wQ==", "signatures": [{"sig": "MEYCIQD5Z5bu3ScY3lJl13T/XQ0SkOPJYktkJrGUuticq5Sb/AIhAJBenEg+lAo+LxvDF6qraJkJriqhO2nYR4B/L/bX3E36", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69150}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "a0e21a5bb91e00ee5297e47c84cc3c7edb976e50", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.2.0_1686891170360_0.5443601740468558", "host": "s3://npm-registry-packages"}}, "16.3.0": {"name": "dotenv", "version": "16.3.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.3.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "1c61ae0a7b6f7b7e2285d50016ebab048f12ec6d", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.3.0.tgz", "fileCount": 11, "integrity": "sha512-tHB+hmf8MRCkT3VVivGiG8kq9HiGTmQ3FzOKgztfpJQH1IWuZTOvKSJmHNnQPowecAmkCJhLrxdPhOr06LLqIQ==", "signatures": [{"sig": "MEYCIQCnZK6yTGsEpjIQbJ+gE5AIbXGXo+sAo9irAo4NKvWhKQIhAKP+FnGxDzrMcUcnAjKwyztJ+n9gBgDQi3qy2ugWS7Ya", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70446}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "76d3682e104f10440c8c6085fe27556f828cb48e", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.3.0_1686935677933_0.9548251586869563", "host": "s3://npm-registry-packages"}}, "16.3.1": {"name": "dotenv", "version": "16.3.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.3.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "369034de7d7e5b120972693352a3bf112172cc3e", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.3.1.tgz", "fileCount": 11, "integrity": "sha512-IPzF4w4/Rd94bA9imS68tZBaYyBWSCE47V1RGuMrB94iyTOIEwRmVL2x/4An+6mETpLrKJ5hQkB8W4kFAadeIQ==", "signatures": [{"sig": "MEYCIQDz0KYz6FtA1MToxzVr/wzRitLUolvSxAB+9m0rRkdbxgIhAMjBc0KqlmvbzPcX2yRUGWJGJZHQ365isJxPdsTu1JN7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71560}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "b13ca7bbdae868e2565db1188a033761ff59cb47", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.3.1_1687020473781_0.16975699256676546", "host": "s3://npm-registry-packages"}}, "16.3.2": {"name": "dotenv", "version": "16.3.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.3.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "3cb611ce5a63002dbabf7c281bc331f69d28f03f", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.3.2.tgz", "fileCount": 11, "integrity": "sha512-HTlk5nmhkm8F6JcdXvHIzaorzCoziNQT9mGxLPVXW8wJF1TiGSL60ZGB4gHWabHOaMmWmhvk2/lPHfnBiT78AQ==", "signatures": [{"sig": "MEQCIBNEeOG3XvRQEfctsnKwPeEEaFu89VmpDR1Hwu0CbkkFAiA8QjTq0/kzkKSsoijJKWidGLL9nRBWZJetlmXQE9qD7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72078}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "883d6a659b9d43f22ae1530bd9dd605fe880f73f", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.3.2_1705684204509_0.6243612667032705", "host": "s3://npm-registry-packages"}}, "16.4.0": {"name": "dotenv", "version": "16.4.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "ac21c3fcaad2e7832a1cd0c0e4e8e52225ecda0e", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.0.tgz", "fileCount": 11, "integrity": "sha512-WvImr5kpN5NGNn7KaDjJnLTh5rDVLZiDf/YLA8T1ZEZEBZNEDOE+mnkS0PVjPax8ZxBP5zC5SLMB3/9VV5de9g==", "signatures": [{"sig": "MEYCIQCpWuN1dvjtfvA5heB3/LDmpegLbrYtHuM1kMyBagVuygIhANdjd3VS1zib9DH7WKtKKHk3cq7uYfYxPMHLe9jvjCfZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74347}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "1259e300e297425f9336b7995a3b82449e949518", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.0_1706040619091_0.8056505212366478", "host": "s3://npm-registry-packages"}}, "16.4.1": {"name": "dotenv", "version": "16.4.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "1d9931f1d3e5d2959350d1250efab299561f7f11", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.1.tgz", "fileCount": 11, "integrity": "sha512-CjA3y+Dr3FyFDOAMnxZEGtnW9KBR2M0JvvUtXNW+dYJL5ROWxP9DUHCwgFqpMk0OXCc0ljhaNTr2w/kutYIcHQ==", "signatures": [{"sig": "MEUCIQCpW0XQkzaInxwvh54ZLIBSp4vtT7G/dt0rk0BFxIiI8QIgU0h2XhDn+1HAARnOQGHzohlGhL6xgxxk6SidI44d2fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76016}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "gitHead": "e251ee244a77fc8f6100d0efaae87ca561f5e33a", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.1_1706121615850_0.41630985689733646", "host": "s3://npm-registry-packages"}}, "16.4.2": {"name": "dotenv", "version": "16.4.2", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.2", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "7ca798f89ae2011846bbdbf6470785307754120d", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.2.tgz", "fileCount": 11, "integrity": "sha512-rZSSFxke7d9nYQ5NeMIwp5PP+f8wXgKNljpOb7KtH6SKW1cEqcXAz9VSJYVLKe7Jhup/gUYOkaeSVyK8GJ+nBg==", "signatures": [{"sig": "MEQCIHIJuqsrZvVvGLn6w4WVBfnRT3BXRhRLncEjcVe/PFuTAiBxkDtbQNYt7VB1pQ+8jRpy1vjmcIJq+GMxBAajKrfcoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77186}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "07948f7e1b6e6e1069be44b465bd7e9406487501", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.2_1707590598018_0.15206092397071425", "host": "s3://npm-registry-packages"}}, "16.4.3": {"name": "dotenv", "version": "16.4.3", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.3", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "481235ec516c4e47d2612a478482ee36607f70c1", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.3.tgz", "fileCount": 11, "integrity": "sha512-II98GFrje5psQTSve0E7bnwMFybNLqT8Vu8JIFWRjsE3khyNUm/loZupuy5DVzG2IXf/ysxvrixYOQnM6mjD3A==", "signatures": [{"sig": "MEYCIQD0ip8N5o9Q2DjbxPdTXhKlKggRnFQW95YcR9mseqFjyQIhAKv2XD7oCl8usFlsSb9+IPKb87onNe+Hcum+bWVSh/fS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78731}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "6581b54d7dfdcfa331f3c1af8cbb5948fcdf44c1", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown", "test:coverage": "tap --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.3_1707768132980_0.043831829213837104", "host": "s3://npm-registry-packages"}}, "16.4.4": {"name": "dotenv", "version": "16.4.4", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.4", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "a26e7bb95ebd36272ebb56edb80b826aecf224c1", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.4.tgz", "fileCount": 11, "integrity": "sha512-XvPXc8XAQThSjAbY6cQ/9PcBXmFoWuw1sQ3b8HqUCR6ziGXjkTi//kB9SWa2UwqlgdAIuRqAa/9hVljzPehbYg==", "signatures": [{"sig": "MEUCIQCD+we40+TWg/BQabXlNTvWEV48AwA/wXuEVA/gV1AVoQIgO7tTR9AMrj78HiJhgF/6eVfVcLfxjerktzphhVKnx1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78984}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "d03e39794ac29aa7e7fde20492b0b8c51544d9d7", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown", "test:coverage": "tap --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.4_1707846353570_0.32976399466205475", "host": "s3://npm-registry-packages"}}, "16.4.5": {"name": "dotenv", "version": "16.4.5", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.5", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "cdd3b3b604cb327e286b4762e13502f717cb099f", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.5.tgz", "fileCount": 11, "integrity": "sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==", "signatures": [{"sig": "MEQCIA/uhk187PCX+a014Xxl/ldDqQu1avSRid6+PGgrkgy7AiAvpn749CsTPfw1tEV3zbXI5q/s8KwT5zLDz+5qH4Wrpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79078}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "9f3e83352ec6ba912161748a2fd15b07829430e2", "scripts": {"lint": "standard", "test": "tap tests/*.js --100 -Rspec", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "lint-readme": "standard-markdown", "test:coverage": "tap --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "tar": "^6.1.11", "sinon": "^14.0.1", "decache": "^4.6.1", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0", "standard-markdown": "^7.1.0", "@definitelytyped/dtslint": "^0.0.133"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.5_1708400018345_0.9628614769430359", "host": "s3://npm-registry-packages"}}, "16.4.6": {"name": "dotenv", "version": "16.4.6", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.6", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "fc88e8a664087abf3e19d61e21f7feee1849bbb1", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.6.tgz", "fileCount": 36, "integrity": "sha512-JhcR/+KIjkkjiU8yEpaB/USlzVi3i5whwOjpIRNGi9svKEXZSe+Qp6IWAjFjv+2GViAoDRCUv/QLNziQxsLqDg==", "signatures": [{"sig": "MEYCIQD1HO0Q+MAlud8Kh68fDEh4VYg/plLEk5cuqM+nEALUkwIhAOiKZahHjpUIS5qubAexTxGk6MJF87dsxs062MwTTol3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360629}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "0c9f764c66f291a418185068eef2b13e2ec840b0", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "22.4.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.6_1733189912625_0.7689976866627639", "host": "s3://npm-registry-packages"}}, "16.4.7": {"name": "dotenv", "version": "16.4.7", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.4.7", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0e20c5b82950140aa99be360a8a5f52335f53c26", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz", "fileCount": 11, "integrity": "sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==", "signatures": [{"sig": "MEYCIQDREajYraYIQec2qcp2tleeBBE8d9b6WheF5IS6tdlZcQIhAI6LaGGQ/Rmk/D91Z5adYJPoRU1UlYmAqPtkHNKfiocA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "a338d68264b00fafd1730233ff04127228908e9c", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "22.4.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.4.7_1733246498858_0.49761131973158523", "host": "s3://npm-registry-packages"}}, "16.5.0": {"name": "dotenv", "version": "16.5.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.5.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "092b49f25f808f020050051d1ff258e404c78692", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz", "fileCount": 11, "integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==", "signatures": [{"sig": "MEUCIBHv79KOrC9DPwjXnu/l8rUbw9wQiKjw4MHYBiLOgfOwAiEA1BqKBw4FL4vdpmwpPaBzwwQYpjCWSqkGAC9p5JTPbBk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75565}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "d39cc9a94ef80b534d02401ab99d17463e902385", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.5.0_1744323279977_0.3888829790843966", "host": "s3://npm-registry-packages-npm-production"}}, "16.6.0": {"name": "dotenv", "version": "16.6.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.6.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "b96bd4e7c2043ba5f51cbe1b8f9347850c864850", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.6.0.tgz", "fileCount": 12, "integrity": "sha512-Omf1L8paOy2VJhILjyhrhqwLIdstqm1BvcDPKg4NGAlkwEu9ODyrFbvk8UymUOMCT+HXo31jg1lArIrVAAhuGA==", "signatures": [{"sig": "MEUCIBwGjagQqg1wlk3l3qK5Hy1zslF3Sf7XcrRfd/nELiq/AiEAy5l8MMgUtpO2u2zP9VFmCUUaGcqKZwy0qlR7bHFW1wo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75726}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "064edcb30aa2390b9463312c2cfadf7946aee2ae", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.6.0_1750963902504_0.676559670351514", "host": "s3://npm-registry-packages-npm-production"}}, "16.6.1": {"name": "dotenv", "version": "16.6.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@16.6.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "773f0e69527a8315c7285d5ee73c4459d20a8020", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz", "fileCount": 12, "integrity": "sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==", "signatures": [{"sig": "MEUCID9x0IciDtj5fx4PNRaMIs0wc0PU1npSHXbjuQGnHAMDAiEAluHBYa/GuInHURabQjPRttlMPtx8GnBXXMI5ULMn4aE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76591}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "076ba3b6a225b8cb878c1cd0a222674b6e5ef87d", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_16.6.1_1751042700883_0.9979093789579256", "host": "s3://npm-registry-packages-npm-production"}}, "17.0.0": {"name": "dotenv", "version": "17.0.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@17.0.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "0b0336dc1a4237bcfa772e01d5587e98ebb7e3c2", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-17.0.0.tgz", "fileCount": 12, "integrity": "sha512-A0BJ5lrpJVSfnMMXjmeO0xUnoxqsBHWCoqqTnGwGYVdnctqXXUEhJOO7LxmgxJon9tEZFGpe0xPRX0h2v3AANQ==", "signatures": [{"sig": "MEUCIQCGEL3+EACdn9UG/HJxxhvmxNGSplTeE4MeXMlcOYc4rwIgNykrNgAd5zA5GbDFNXUmr/Qo46tpRw/eei7iBzUbGAM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 77175}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "f28bfd55817054af9fd5097b19eb4faa2189cb6b", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_17.0.0_1751056627999_0.9580924851984542", "host": "s3://npm-registry-packages-npm-production"}}, "17.0.1": {"name": "dotenv", "version": "17.0.1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@17.0.1", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "79bc4d232fadb42a4092685ff1206d31b2a43f95", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-17.0.1.tgz", "fileCount": 12, "integrity": "sha512-GLjkduuAL7IMJg/ZnOPm9AnWKJ82mSE2tzXLaJ/6hD6DhwGfZaXG77oB8qbReyiczNxnbxQKyh0OE5mXq0bAHA==", "signatures": [{"sig": "MEQCIACFs6rpQ33b3c8P4vz69Kos4eSTXymMAMByXKXB7yKsAiABDydgC2OZUYcNIskpgUHL/YhqY6Bn6mJqoOTlc4IRTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76506}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "535465c6f9f2c3554adb6e1b66ee9633c8ddf15b", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_17.0.1_1751384294455_0.7103373566041307", "host": "s3://npm-registry-packages-npm-production"}}, "17.1.0": {"name": "dotenv", "version": "17.1.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@17.1.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "164fa6ac1392165f5e2d8eb55296b0894e7d6a22", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-17.1.0.tgz", "fileCount": 12, "integrity": "sha512-tG9VUTJTuju6GcXgbdsOuRhupE8cb4mRgY5JLRCh4MtGoVo3/gfGUtOMwmProM6d0ba2mCFvv+WrpYJV6qgJXQ==", "signatures": [{"sig": "MEUCIQDUQmxPjlyUwtd7l1Qf2cmVvB1tErejMg0IL2lhQs/j7AIgPuWBxEKy/cs3ZMnnIY1lnaVVhikxvtbSE6oN94PLR+Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78848}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "13bc310c4854c336ce7a0949e5e622e57d9d4bdf", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_17.1.0_1751933490544_0.9743890509132089", "host": "s3://npm-registry-packages-npm-production"}}, "17.2.0": {"name": "dotenv", "version": "17.2.0", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "_id": "dotenv@17.2.0", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "homepage": "https://github.com/motdotla/dotenv#readme", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "dist": {"shasum": "e19678fdabcf86d4bfdb6764a758d7d44efbb6a2", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-17.2.0.tgz", "fileCount": 12, "integrity": "sha512-Q4sgBT60gzd0BB0lSyYD3xM4YxrXA9y4uBDof1JNYGzOXrQdQ6yX+7XIAqoFOGQFOTK1D3Hts5OllpxMDZFONQ==", "signatures": [{"sig": "MEYCIQCeoyIEmBOYDvmRU5ZwTYHA8+DSox0OU0rRhcun8L06nwIhAPL+qCls00BZi1jd0oDTEry1+9S/3fFyT2YQFiqRmMgg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 80002}, "main": "lib/main.js", "types": "lib/main.d.ts", "browser": {"fs": false}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "default": "./lib/main.js", "require": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./package.json": "./package.json", "./lib/cli-options": "./lib/cli-options.js", "./lib/env-options": "./lib/env-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./lib/env-options.js": "./lib/env-options.js"}, "funding": "https://dotenvx.com", "gitHead": "11acd9fc33ee81b2bfbf8ef5924c800a7454a8dd", "scripts": {"lint": "standard", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "dts-check": "tsc --project tests/types/tsconfig.json", "prerelease": "npm test", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "_npmUser": {"name": "motdotla", "actor": {"name": "motdotla", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git://github.com/motdotla/dotenv.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Loads environment variables from .env file", "directories": {}, "_nodeVersion": "23.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.2.0", "sinon": "^14.0.1", "decache": "^4.6.2", "standard": "^17.0.0", "typescript": "^4.8.4", "@types/node": "^18.11.3", "standard-version": "^9.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/dotenv_17.2.0_1752078340804_0.11572297069078807", "host": "s3://npm-registry-packages-npm-production"}}, "17.2.1": {"name": "dotenv", "version": "17.2.1", "description": "Loads environment variables from .env file", "main": "lib/main.js", "types": "lib/main.d.ts", "exports": {".": {"types": "./lib/main.d.ts", "require": "./lib/main.js", "default": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./lib/env-options": "./lib/env-options.js", "./lib/env-options.js": "./lib/env-options.js", "./lib/cli-options": "./lib/cli-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./package.json": "./package.json"}, "scripts": {"dts-check": "tsc --project tests/types/tsconfig.json", "lint": "standard", "pretest": "npm run lint && npm run dts-check", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov", "prerelease": "npm test", "release": "standard-version"}, "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "homepage": "https://github.com/motdotla/dotenv#readme", "funding": "https://dotenvx.com", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@types/node": "^18.11.3", "decache": "^4.6.2", "sinon": "^14.0.1", "standard": "^17.0.0", "standard-version": "^9.5.0", "tap": "^19.2.0", "typescript": "^4.8.4"}, "engines": {"node": ">=12"}, "browser": {"fs": false}, "_id": "dotenv@17.2.1", "gitHead": "37cf55a092036ba23d9435a682e8b1ceb4f329e8", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-kQhDYKZecqnM0fCnzI5eIv5L4cAe/iRI+HqMbO/hbRdTAeXDG+M9FjipUxNfbARuEg4iHIbhnhs78BCHNbSxEQ==", "shasum": "6f32e10faf014883515538dc922a0fb8765d9b32", "tarball": "https://registry.npmjs.org/dotenv/-/dotenv-17.2.1.tgz", "fileCount": 12, "unpackedSize": 80536, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCID0+GQsmkBdSYc5eH77ABvNm+LGsB5KJLO35ZPBUFUznAiEAtDdsBZlmlFHJ+BW/wBZjlLSiqhAdd+AaYgI5eE0hCcA="}]}, "_npmUser": {"name": "motdotla", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/dotenv_17.2.1_1753389926180_0.925902174869516"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-05T18:29:15.210Z", "modified": "2025-07-24T20:45:26.576Z", "0.0.1": "2013-07-05T18:29:16.165Z", "0.0.2": "2013-07-10T18:17:20.177Z", "0.0.3": "2013-08-23T19:21:40.834Z", "0.0.4": "2013-10-31T01:59:00.996Z", "0.0.5": "2013-10-31T02:03:56.495Z", "0.1.0": "2013-11-03T21:33:18.368Z", "0.1.1": "2013-11-03T22:05:37.280Z", "0.1.2": "2013-11-03T22:33:10.087Z", "0.2.0": "2013-11-03T23:30:02.648Z", "0.2.1": "2013-11-15T01:13:51.431Z", "0.2.2": "2013-11-18T23:11:45.413Z", "0.2.3": "2013-11-19T00:37:12.822Z", "0.2.4": "2014-02-08T17:29:26.506Z", "0.2.5": "2014-02-28T04:41:11.774Z", "0.2.6": "2014-03-27T18:41:27.941Z", "0.2.7": "2014-03-27T21:52:47.488Z", "0.2.8": "2014-04-17T22:08:47.967Z", "0.3.0": "2014-06-24T02:43:23.653Z", "0.4.0": "2014-06-28T00:52:56.077Z", "0.5.0": "2015-01-27T19:41:03.151Z", "0.5.1": "2015-01-28T01:57:45.290Z", "1.0.0": "2015-03-14T00:27:55.042Z", "1.1.0": "2015-04-01T04:54:38.095Z", "1.2.0": "2015-06-21T05:55:08.633Z", "2.0.0": "2016-01-21T00:49:28.956Z", "3.0.0": "2017-01-07T19:08:24.083Z", "4.0.0": "2017-01-07T19:08:54.778Z", "5.0.0": "2018-01-29T23:15:10.806Z", "5.0.1": "2018-02-26T20:46:29.846Z", "6.0.0": "2018-06-02T18:10:51.291Z", "6.1.0": "2018-10-08T23:58:46.272Z", "6.2.0-rc1": "2018-11-01T21:35:36.927Z", "6.2.0-0": "2018-12-04T05:37:05.011Z", "6.2.0": "2018-12-05T18:28:53.135Z", "7.0.0": "2019-03-13T06:06:49.773Z", "8.0.0": "2019-05-02T22:28:49.003Z", "8.1.0": "2019-08-18T03:32:43.514Z", "8.2.0": "2019-10-16T01:50:46.096Z", "8.3.0": "2021-05-05T03:03:24.850Z", "8.4.0": "2021-05-05T03:37:10.079Z", "8.5.0": "2021-05-05T06:05:16.517Z", "8.5.1": "2021-05-05T06:42:12.460Z", "8.6.0": "2021-05-05T15:42:36.577Z", "9.0.0": "2021-05-05T17:15:14.992Z", "9.0.1": "2021-05-09T06:23:36.106Z", "9.0.2": "2021-05-10T18:27:11.556Z", "10.0.0": "2021-05-21T19:40:25.388Z", "11.0.0": "2022-01-11T06:59:59.131Z", "12.0.0": "2022-01-14T23:56:06.330Z", "12.0.1": "2022-01-15T00:50:52.458Z", "12.0.2": "2022-01-15T01:22:00.221Z", "12.0.3": "2022-01-15T01:36:45.543Z", "12.0.4": "2022-01-16T08:50:57.589Z", "13.0.0": "2022-01-16T22:49:24.978Z", "13.0.1": "2022-01-16T23:53:30.043Z", "14.0.0": "2022-01-17T03:51:39.841Z", "14.0.1": "2022-01-17T04:50:41.443Z", "14.1.0": "2022-01-17T05:54:18.094Z", "14.1.1": "2022-01-17T20:24:27.531Z", "14.2.0": "2022-01-17T21:13:04.544Z", "14.3.0": "2022-01-24T21:09:28.758Z", "14.3.1": "2022-01-25T21:46:41.257Z", "14.3.2": "2022-01-25T22:19:37.962Z", "15.0.0": "2022-01-31T05:06:16.356Z", "15.0.1": "2022-02-02T20:09:03.687Z", "16.0.0": "2022-02-02T21:26:07.108Z", "16.0.1": "2022-05-10T19:11:40.976Z", "16.0.2": "2022-08-30T19:04:03.212Z", "16.0.3": "2022-09-29T17:28:10.605Z", "16.1.0-rc1": "2023-04-07T22:26:12.934Z", "16.1.0-rc2": "2023-05-21T05:12:14.198Z", "16.1.0": "2023-05-30T16:17:27.158Z", "16.1.1": "2023-05-31T01:41:30.628Z", "16.1.2": "2023-05-31T15:28:14.482Z", "16.1.3": "2023-05-31T19:01:19.739Z", "16.1.4": "2023-06-04T15:50:24.764Z", "16.2.0": "2023-06-16T04:52:50.541Z", "16.3.0": "2023-06-16T17:14:38.112Z", "16.3.1": "2023-06-17T16:47:53.971Z", "16.3.2": "2024-01-19T17:10:04.696Z", "16.4.0": "2024-01-23T20:10:19.275Z", "16.4.1": "2024-01-24T18:40:15.995Z", "16.4.2": "2024-02-10T18:43:18.207Z", "16.4.3": "2024-02-12T20:02:13.155Z", "16.4.4": "2024-02-13T17:45:53.790Z", "16.4.5": "2024-02-20T03:33:38.563Z", "16.4.6": "2024-12-03T01:38:32.817Z", "16.4.7": "2024-12-03T17:21:39.065Z", "16.5.0": "2025-04-10T22:14:40.154Z", "16.6.0": "2025-06-26T18:51:42.723Z", "16.6.1": "2025-06-27T16:45:01.088Z", "17.0.0": "2025-06-27T20:37:08.187Z", "17.0.1": "2025-07-01T15:38:14.640Z", "17.1.0": "2025-07-08T00:11:30.775Z", "17.2.0": "2025-07-09T16:25:40.977Z", "17.2.1": "2025-07-24T20:45:26.391Z"}, "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/motdotla/dotenv#readme", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "description": "Loads environment variables from .env file", "maintainers": [{"name": "~jcblw", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "motdotla", "email": "<EMAIL>"}, {"name": "motdotenv", "email": "<EMAIL>"}], "readme": "<div align=\"center\">\n🎉 announcing <a href=\"https://github.com/dotenvx/dotenvx\">dotenvx</a>. <em>run anywhere, multi-environment, encrypted envs</em>.\n</div>\n\n&nbsp;\n\n# dotenv [![NPM version](https://img.shields.io/npm/v/dotenv.svg?style=flat-square)](https://www.npmjs.com/package/dotenv)\n\n<img src=\"https://raw.githubusercontent.com/motdotla/dotenv/master/dotenv.svg\" alt=\"dotenv\" align=\"right\" width=\"200\" />\n\nDotenv is a zero-dependency module that loads environment variables from a `.env` file into [`process.env`](https://nodejs.org/docs/latest/api/process.html#process_process_env). Storing configuration in the environment separate from code is based on [The Twelve-Factor App](https://12factor.net/config) methodology.\n\n[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg?style=flat-square)](https://github.com/feross/standard)\n[![LICENSE](https://img.shields.io/github/license/motdotla/dotenv.svg)](LICENSE)\n[![codecov](https://codecov.io/gh/motdotla/dotenv-expand/graph/badge.svg?token=pawWEyaMfg)](https://codecov.io/gh/motdotla/dotenv-expand)\n\n* [🌱 Install](#-install)\n* [🏗️ Usage (.env)](#%EF%B8%8F-usage)\n* [🌴 Multiple Environments 🆕](#-manage-multiple-environments)\n* [🚀 Deploying (encryption) 🆕](#-deploying)\n* [📚 Examples](#-examples)\n* [📖 Docs](#-documentation)\n* [❓ FAQ](#-faq)\n* [⏱️ Changelog](./CHANGELOG.md)\n\n## 🌱 Install\n\n```bash\nnpm install dotenv --save\n```\n\nYou can also use an npm-compatible package manager like yarn, bun or pnpm:\n\n```bash\nyarn add dotenv\n```\n```bash\nbun add dotenv\n```\n```bash\npnpm add dotenv\n```\n\n## 🏗️ Usage\n\n<a href=\"https://www.youtube.com/watch?v=YtkZR0NFd1g\">\n<div align=\"right\">\n<img src=\"https://img.youtube.com/vi/YtkZR0NFd1g/hqdefault.jpg\" alt=\"how to use dotenv video tutorial\" align=\"right\" width=\"330\" />\n<img src=\"https://simpleicons.vercel.app/youtube/ff0000\" alt=\"youtube/@dotenvorg\" align=\"right\" width=\"24\" />\n</div>\n</a>\n\nCreate a `.env` file in the root of your project (if using a monorepo structure like `apps/backend/app.js`, put it in the root of the folder where your `app.js` process runs):\n\n```dosini\nS3_BUCKET=\"YOURS3BUCKET\"\nSECRET_KEY=\"YOURSECRETKEYGOESHERE\"\n```\n\nAs early as possible in your application, import and configure dotenv:\n\n```javascript\nrequire('dotenv').config()\nconsole.log(process.env) // remove this after you've confirmed it is working\n```\n\n.. [or using ES6?](#how-do-i-use-dotenv-with-import)\n\n```javascript\nimport 'dotenv/config'\n```\n\nES6 import if you need to set config options:\n\n```javascript\nimport dotenv from 'dotenv'\n\ndotenv.config({ path: '/custom/path/to/.env' })\n```\n\nThat's it. `process.env` now has the keys and values you defined in your `.env` file:\n\n```javascript\nrequire('dotenv').config()\n// or import 'dotenv/config' if you're using ES6\n\n...\n\ns3.getBucketCors({Bucket: process.env.S3_BUCKET}, function(err, data) {})\n```\n\n### Multiline values\n\nIf you need multiline variables, for example private keys, those are now supported (`>= v15.0.0`) with line breaks:\n\n```dosini\nPRIVATE_KEY=\"-----BEGIN RSA PRIVATE KEY-----\n...\nKh9NV...\n...\n-----END RSA PRIVATE KEY-----\"\n```\n\nAlternatively, you can double quote strings and use the `\\n` character:\n\n```dosini\nPRIVATE_KEY=\"-----BEGIN RSA PRIVATE KEY-----\\nKh9NV...\\n-----END RSA PRIVATE KEY-----\\n\"\n```\n\n### Comments\n\nComments may be added to your file on their own line or inline:\n\n```dosini\n# This is a comment\nSECRET_KEY=YOURSECRETKEYGOESHERE # comment\nSECRET_HASH=\"something-with-a-#-hash\"\n```\n\nComments begin where a `#` exists, so if your value contains a `#` please wrap it in quotes. This is a breaking change from `>= v15.0.0` and on.\n\n### Parsing\n\nThe engine which parses the contents of your file containing environment variables is available to use. It accepts a String or Buffer and will return an Object with the parsed keys and values.\n\n```javascript\nconst dotenv = require('dotenv')\nconst buf = Buffer.from('BASIC=basic')\nconst config = dotenv.parse(buf) // will return an object\nconsole.log(typeof config, config) // object { BASIC : 'basic' }\n```\n\n### Preload\n\n> Note: Consider using [`dotenvx`](https://github.com/dotenvx/dotenvx) instead of preloading. I am now doing (and recommending) so.\n>\n> It serves the same purpose (you do not need to require and load dotenv), adds better debugging, and works with ANY language, framework, or platform. – [motdotla](https://github.com/motdotla)\n\nYou can use the `--require` (`-r`) [command line option](https://nodejs.org/api/cli.html#-r---require-module) to preload dotenv. By doing this, you do not need to require and load dotenv in your application code.\n\n```bash\n$ node -r dotenv/config your_script.js\n```\n\nThe configuration options below are supported as command line arguments in the format `dotenv_config_<option>=value`\n\n```bash\n$ node -r dotenv/config your_script.js dotenv_config_path=/custom/path/to/.env dotenv_config_debug=true\n```\n\nAdditionally, you can use environment variables to set configuration options. Command line arguments will precede these.\n\n```bash\n$ DOTENV_CONFIG_<OPTION>=value node -r dotenv/config your_script.js\n```\n\n```bash\n$ DOTENV_CONFIG_ENCODING=latin1 DOTENV_CONFIG_DEBUG=true node -r dotenv/config your_script.js dotenv_config_path=/custom/path/to/.env\n```\n\n### Variable Expansion\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx) to use variable expansion.\n\nReference and expand variables already on your machine for use in your .env file.\n\n```ini\n# .env\nUSERNAME=\"username\"\nDATABASE_URL=\"postgres://${USERNAME}@localhost/my_database\"\n```\n```js\n// index.js\nconsole.log('DATABASE_URL', process.env.DATABASE_URL)\n```\n```sh\n$ dotenvx run --debug -- node index.js\n[dotenvx@0.14.1] injecting env (2) from .env\nDATABASE_URL postgres://username@localhost/my_database\n```\n\n### Command Substitution\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx) to use command substitution.\n\nAdd the output of a command to one of your variables in your .env file.\n\n```ini\n# .env\nDATABASE_URL=\"postgres://$(whoami)@localhost/my_database\"\n```\n```js\n// index.js\nconsole.log('DATABASE_URL', process.env.DATABASE_URL)\n```\n```sh\n$ dotenvx run --debug -- node index.js\n[dotenvx@0.14.1] injecting env (1) from .env\nDATABASE_URL postgres://yourusername@localhost/my_database\n```\n\n### Syncing\n\nYou need to keep `.env` files in sync between machines, environments, or team members? Use [dotenvx](https://github.com/dotenvx/dotenvx) to encrypt your `.env` files and safely include them in source control. This still subscribes to the twelve-factor app rules by generating a decryption key separate from code.\n\n### Multiple Environments\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx) to generate `.env.ci`, `.env.production` files, and more.\n\n### Deploying\n\nYou need to deploy your secrets in a cloud-agnostic manner? Use [dotenvx](https://github.com/dotenvx/dotenvx) to generate a private decryption key that is set on your production server.\n\n## 🌴 Manage Multiple Environments\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx)\n\nRun any environment locally. Create a `.env.ENVIRONMENT` file and use `--env-file` to load it. It's straightforward, yet flexible.\n\n```bash\n$ echo \"HELLO=production\" > .env.production\n$ echo \"console.log('Hello ' + process.env.HELLO)\" > index.js\n\n$ dotenvx run --env-file=.env.production -- node index.js\nHello production\n> ^^\n```\n\nor with multiple .env files\n\n```bash\n$ echo \"HELLO=local\" > .env.local\n$ echo \"HELLO=World\" > .env\n$ echo \"console.log('Hello ' + process.env.HELLO)\" > index.js\n\n$ dotenvx run --env-file=.env.local --env-file=.env -- node index.js\nHello local\n```\n\n[more environment examples](https://dotenvx.com/docs/quickstart/environments)\n\n## 🚀 Deploying\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx).\n\nAdd encryption to your `.env` files with a single command. Pass the `--encrypt` flag.\n\n```\n$ dotenvx set HELLO Production --encrypt -f .env.production\n$ echo \"console.log('Hello ' + process.env.HELLO)\" > index.js\n\n$ DOTENV_PRIVATE_KEY_PRODUCTION=\"<.env.production private key>\" dotenvx run -- node index.js\n[dotenvx] injecting env (2) from .env.production\nHello Production\n```\n\n[learn more](https://github.com/dotenvx/dotenvx?tab=readme-ov-file#encryption)\n\n## 📚 Examples\n\nSee [examples](https://github.com/dotenv-org/examples) of using dotenv with various frameworks, languages, and configurations.\n\n* [nodejs](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-nodejs)\n* [nodejs (debug on)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-nodejs-debug)\n* [nodejs (override on)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-nodejs-override)\n* [nodejs (processEnv override)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-custom-target)\n* [esm](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-esm)\n* [esm (preload)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-esm-preload)\n* [typescript](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-typescript)\n* [typescript parse](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-typescript-parse)\n* [typescript config](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-typescript-config)\n* [webpack](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-webpack)\n* [webpack (plugin)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-webpack2)\n* [react](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-react)\n* [react (typescript)](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-react-typescript)\n* [express](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-express)\n* [nestjs](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-nestjs)\n* [fastify](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-fastify)\n\n## 📖 Documentation\n\nDotenv exposes four functions:\n\n* `config`\n* `parse`\n* `populate`\n\n### Config\n\n`config` will read your `.env` file, parse the contents, assign it to\n[`process.env`](https://nodejs.org/docs/latest/api/process.html#process_process_env),\nand return an Object with a `parsed` key containing the loaded content or an `error` key if it failed.\n\n```js\nconst result = dotenv.config()\n\nif (result.error) {\n  throw result.error\n}\n\nconsole.log(result.parsed)\n```\n\nYou can additionally, pass options to `config`.\n\n#### Options\n\n##### path\n\nDefault: `path.resolve(process.cwd(), '.env')`\n\nSpecify a custom path if your file containing environment variables is located elsewhere.\n\n```js\nrequire('dotenv').config({ path: '/custom/path/to/.env' })\n```\n\nBy default, `config` will look for a file called .env in the current working directory.\n\nPass in multiple files as an array, and they will be parsed in order and combined with `process.env` (or `option.processEnv`, if set). The first value set for a variable will win, unless the `options.override` flag is set, in which case the last value set will win.  If a value already exists in `process.env` and the `options.override` flag is NOT set, no changes will be made to that value. \n\n```js  \nrequire('dotenv').config({ path: ['.env.local', '.env'] })\n```\n\n##### quiet\n\nDefault: `false`\n\nSuppress runtime logging message.\n\n```js\n// index.js\nrequire('dotenv').config({ quiet: false }) // change to true to suppress\nconsole.log(`Hello ${process.env.HELLO}`)\n```\n\n```ini\n# .env\n.env\n```\n\n```sh\n$ node index.js\n[dotenv@17.0.0] injecting env (1) from .env\nHello World\n```\n\n##### encoding\n\nDefault: `utf8`\n\nSpecify the encoding of your file containing environment variables.\n\n```js\nrequire('dotenv').config({ encoding: 'latin1' })\n```\n\n##### debug\n\nDefault: `false`\n\nTurn on logging to help debug why certain keys or values are not being set as you expect.\n\n```js\nrequire('dotenv').config({ debug: process.env.DEBUG })\n```\n\n##### override\n\nDefault: `false`\n\nOverride any environment variables that have already been set on your machine with values from your .env file(s). If multiple files have been provided in `option.path` the override will also be used as each file is combined with the next. Without `override` being set, the first value wins. With `override` set the last value wins. \n\n```js\nrequire('dotenv').config({ override: true })\n```\n\n##### processEnv\n\nDefault: `process.env`\n\nSpecify an object to write your environment variables to. Defaults to `process.env` environment variables.\n\n```js\nconst myObject = {}\nrequire('dotenv').config({ processEnv: myObject })\n\nconsole.log(myObject) // values from .env\nconsole.log(process.env) // this was not changed or written to\n```\n\n### Parse\n\nThe engine which parses the contents of your file containing environment\nvariables is available to use. It accepts a String or Buffer and will return\nan Object with the parsed keys and values.\n\n```js\nconst dotenv = require('dotenv')\nconst buf = Buffer.from('BASIC=basic')\nconst config = dotenv.parse(buf) // will return an object\nconsole.log(typeof config, config) // object { BASIC : 'basic' }\n```\n\n#### Options\n\n##### debug\n\nDefault: `false`\n\nTurn on logging to help debug why certain keys or values are not being set as you expect.\n\n```js\nconst dotenv = require('dotenv')\nconst buf = Buffer.from('hello world')\nconst opt = { debug: true }\nconst config = dotenv.parse(buf, opt)\n// expect a debug message because the buffer is not in KEY=VAL form\n```\n\n### Populate\n\nThe engine which populates the contents of your .env file to `process.env` is available for use. It accepts a target, a source, and options. This is useful for power users who want to supply their own objects.\n\nFor example, customizing the source:\n\n```js\nconst dotenv = require('dotenv')\nconst parsed = { HELLO: 'world' }\n\ndotenv.populate(process.env, parsed)\n\nconsole.log(process.env.HELLO) // world\n```\n\nFor example, customizing the source AND target:\n\n```js\nconst dotenv = require('dotenv')\nconst parsed = { HELLO: 'universe' }\nconst target = { HELLO: 'world' } // empty object\n\ndotenv.populate(target, parsed, { override: true, debug: true })\n\nconsole.log(target) // { HELLO: 'universe' }\n```\n\n#### options\n\n##### Debug\n\nDefault: `false`\n\nTurn on logging to help debug why certain keys or values are not being populated as you expect.\n\n##### override\n\nDefault: `false`\n\nOverride any environment variables that have already been set.\n\n## ❓ FAQ\n\n### Why is the `.env` file not loading my environment variables successfully?\n\nMost likely your `.env` file is not in the correct place. [See this stack overflow](https://stackoverflow.com/questions/42335016/dotenv-file-is-not-loading-environment-variables).\n\nTurn on debug mode and try again..\n\n```js\nrequire('dotenv').config({ debug: true })\n```\n\nYou will receive a helpful error outputted to your console.\n\n### Should I commit my `.env` file?\n\nNo. We **strongly** recommend against committing your `.env` file to version\ncontrol. It should only include environment-specific values such as database\npasswords or API keys. Your production database should have a different\npassword than your development database.\n\n### Should I have multiple `.env` files?\n\nWe recommend creating one `.env` file per environment. Use `.env` for local/development, `.env.production` for production and so on. This still follows the twelve factor principles as each is attributed individually to its own environment. Avoid custom set ups that work in inheritance somehow (`.env.production` inherits values form `.env` for example). It is better to duplicate values if necessary across each `.env.environment` file.\n\n> In a twelve-factor app, env vars are granular controls, each fully orthogonal to other env vars. They are never grouped together as “environments”, but instead are independently managed for each deploy. This is a model that scales up smoothly as the app naturally expands into more deploys over its lifetime.\n>\n> – [The Twelve-Factor App](http://12factor.net/config)\n\n### What rules does the parsing engine follow?\n\nThe parsing engine currently supports the following rules:\n\n- `BASIC=basic` becomes `{BASIC: 'basic'}`\n- empty lines are skipped\n- lines beginning with `#` are treated as comments\n- `#` marks the beginning of a comment (unless when the value is wrapped in quotes)\n- empty values become empty strings (`EMPTY=` becomes `{EMPTY: ''}`)\n- inner quotes are maintained (think JSON) (`JSON={\"foo\": \"bar\"}` becomes `{JSON:\"{\\\"foo\\\": \\\"bar\\\"}\"`)\n- whitespace is removed from both ends of unquoted values (see more on [`trim`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/Trim)) (`FOO=  some value  ` becomes `{FOO: 'some value'}`)\n- single and double quoted values are escaped (`SINGLE_QUOTE='quoted'` becomes `{SINGLE_QUOTE: \"quoted\"}`)\n- single and double quoted values maintain whitespace from both ends (`FOO=\"  some value  \"` becomes `{FOO: '  some value  '}`)\n- double quoted values expand new lines (`MULTILINE=\"new\\nline\"` becomes\n\n```\n{MULTILINE: 'new\nline'}\n```\n\n- backticks are supported (`` BACKTICK_KEY=`This has 'single' and \"double\" quotes inside of it.` ``)\n\n### What happens to environment variables that were already set?\n\nBy default, we will never modify any environment variables that have already been set. In particular, if there is a variable in your `.env` file which collides with one that already exists in your environment, then that variable will be skipped.\n\nIf instead, you want to override `process.env` use the `override` option.\n\n```javascript\nrequire('dotenv').config({ override: true })\n```\n\n### How come my environment variables are not showing up for React?\n\nYour React code is run in Webpack, where the `fs` module or even the `process` global itself are not accessible out-of-the-box. `process.env` can only be injected through Webpack configuration.\n\nIf you are using [`react-scripts`](https://www.npmjs.com/package/react-scripts), which is distributed through [`create-react-app`](https://create-react-app.dev/), it has dotenv built in but with a quirk. Preface your environment variables with `REACT_APP_`. See [this stack overflow](https://stackoverflow.com/questions/42182577/is-it-possible-to-use-dotenv-in-a-react-project) for more details.\n\nIf you are using other frameworks (e.g. Next.js, Gatsby...), you need to consult their documentation for how to inject environment variables into the client.\n\n### Can I customize/write plugins for dotenv?\n\nYes! `dotenv.config()` returns an object representing the parsed `.env` file. This gives you everything you need to continue setting values on `process.env`. For example:\n\n```js\nconst dotenv = require('dotenv')\nconst variableExpansion = require('dotenv-expand')\nconst myEnv = dotenv.config()\nvariableExpansion(myEnv)\n```\n\n### How do I use dotenv with `import`?\n\nSimply..\n\n```javascript\n// index.mjs (ESM)\nimport 'dotenv/config' // see https://github.com/motdotla/dotenv#how-do-i-use-dotenv-with-import\nimport express from 'express'\n```\n\nA little background..\n\n> When you run a module containing an `import` declaration, the modules it imports are loaded first, then each module body is executed in a depth-first traversal of the dependency graph, avoiding cycles by skipping anything already executed.\n>\n> – [ES6 In Depth: Modules](https://hacks.mozilla.org/2015/08/es6-in-depth-modules/)\n\nWhat does this mean in plain language? It means you would think the following would work but it won't.\n\n`errorReporter.mjs`:\n```js\nclass Client {\n  constructor (apiKey) {\n    console.log('apiKey', apiKey)\n\n    this.apiKey = apiKey\n  }\n}\n\nexport default new Client(process.env.API_KEY)\n```\n`index.mjs`:\n```js\n// Note: this is INCORRECT and will not work\nimport * as dotenv from 'dotenv'\ndotenv.config()\n\nimport errorReporter from './errorReporter.mjs' // process.env.API_KEY will be blank!\n```\n\n`process.env.API_KEY` will be blank.\n\nInstead, `index.mjs` should be written as..\n\n```js\nimport 'dotenv/config'\n\nimport errorReporter from './errorReporter.mjs'\n```\n\nDoes that make sense? It's a bit unintuitive, but it is how importing of ES6 modules work. Here is a [working example of this pitfall](https://github.com/dotenv-org/examples/tree/master/usage/dotenv-es6-import-pitfall).\n\nThere are two alternatives to this approach:\n\n1. Preload with dotenvx: `dotenvx run -- node index.js` (_Note: you do not need to `import` dotenv with this approach_)\n2. Create a separate file that will execute `config` first as outlined in [this comment on #133](https://github.com/motdotla/dotenv/issues/133#issuecomment-255298822)\n\n### Why am I getting the error `Module not found: Error: Can't resolve 'crypto|os|path'`?\n\nYou are using dotenv on the front-end and have not included a polyfill. Webpack < 5 used to include these for you. Do the following:\n\n```bash\nnpm install node-polyfill-webpack-plugin\n```\n\nConfigure your `webpack.config.js` to something like the following.\n\n```js\nrequire('dotenv').config()\n\nconst path = require('path');\nconst webpack = require('webpack')\n\nconst NodePolyfillPlugin = require('node-polyfill-webpack-plugin')\n\nmodule.exports = {\n  mode: 'development',\n  entry: './src/index.ts',\n  output: {\n    filename: 'bundle.js',\n    path: path.resolve(__dirname, 'dist'),\n  },\n  plugins: [\n    new NodePolyfillPlugin(),\n    new webpack.DefinePlugin({\n      'process.env': {\n        HELLO: JSON.stringify(process.env.HELLO)\n      }\n    }),\n  ]\n};\n```\n\nAlternatively, just use [dotenv-webpack](https://github.com/mrsteele/dotenv-webpack) which does this and more behind the scenes for you.\n\n### What about variable expansion?\n\nTry [dotenv-expand](https://github.com/motdotla/dotenv-expand)\n\n### What about syncing and securing .env files?\n\nUse [dotenvx](https://github.com/dotenvx/dotenvx) to unlock syncing encrypted .env files over git.\n\n### What if I accidentally commit my `.env` file to code?\n\nRemove it, [remove git history](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/removing-sensitive-data-from-a-repository) and then install the [git pre-commit hook](https://github.com/dotenvx/dotenvx#pre-commit) to prevent this from ever happening again. \n\n```\nbrew install dotenvx/brew/dotenvx\ndotenvx precommit --install\n```\n\n### How can I prevent committing my `.env` file to a Docker build?\n\nUse the [docker prebuild hook](https://dotenvx.com/docs/features/prebuild).\n\n```bash\n# Dockerfile\n...\nRUN curl -fsS https://dotenvx.sh/ | sh\n...\nRUN dotenvx prebuild\nCMD [\"dotenvx\", \"run\", \"--\", \"node\", \"index.js\"]\n```\n\n## Contributing Guide\n\nSee [CONTRIBUTING.md](CONTRIBUTING.md)\n\n## CHANGELOG\n\nSee [CHANGELOG.md](CHANGELOG.md)\n\n## Who's using dotenv?\n\n[These npm modules depend on it.](https://www.npmjs.com/browse/depended/dotenv)\n\nProjects that expand it often use the [keyword \"dotenv\" on npm](https://www.npmjs.com/search?q=keywords:dotenv).\n", "readmeFilename": "README.md", "users": {"*********": true, "gvn": true, "jk0": true, "ubi": true, "awzm": true, "dntx": true, "epan": true, "j.su": true, "japh": true, "jd73": true, "jrop": true, "jtrh": true, "ky23": true, "leny": true, "mrra": true, "n3gu": true, "neo1": true, "qmmr": true, "rdcl": true, "spad": true, "tg-z": true, "usex": true, "vwal": true, "wayn": true, "zapo": true, "andyd": true, "arniu": true, "ashco": true, "bengi": true, "ddffx": true, "ehrig": true, "jream": true, "junos": true, "kikna": true, "lcdss": true, "leapm": true, "lijsh": true, "lusai": true, "madeo": true, "panlw": true, "r3nya": true, "razr9": true, "samar": true, "sjnnr": true, "travm": true, "weerd": true, "xkema": true, "zvovu": true, "0x4c3p": true, "71emj1": true, "ajduke": true, "arttse": true, "atomox": true, "benstr": true, "buster": true, "buzuli": true, "daizch": true, "dbogda": true, "den-dp": true, "dgmike": true, "dkblay": true, "dsiddy": true, "ekmpls": true, "h0ward": true, "hsiang": true, "iusfof": true, "jadnco": true, "jhart9": true, "joe.li": true, "jolg42": true, "joni3k": true, "kbakba": true, "knoja4": true, "kratyk": true, "ksketo": true, "lomocc": true, "mahume": true, "mattms": true, "mcacek": true, "minhna": true, "mjasso": true, "monjer": true, "mrbgit": true, "narven": true, "nauhil": true, "netweb": true, "nhutle": true, "niccai": true, "nsaboo": true, "oblank": true, "ovrmrw": true, "phocks": true, "pirmax": true, "rajiff": true, "rcline": true, "rehf27": true, "rexpan": true, "rianma": true, "romedu": true, "ryaned": true, "sensui": true, "sinfex": true, "smirzo": true, "thefox": true, "udeste": true, "vutran": true, "wisetc": true, "yicone": true, "yursha": true, "zewish": true, "abasdeo": true, "abhutch": true, "ajaegle": true, "akabeko": true, "amaynut": true, "andy007": true, "astesio": true, "ayoungh": true, "benargo": true, "chriszs": true, "copycut": true, "cwagner": true, "dhampik": true, "dr_blue": true, "drafael": true, "ezeikel": true, "ezodude": true, "fainder": true, "fdeneux": true, "filipve": true, "forvais": true, "geekish": true, "gpuente": true, "gugadev": true, "gyaresu": true, "hasssan": true, "hitalos": true, "icodejs": true, "idmytro": true, "itonyyo": true, "jaguarj": true, "jalcine": true, "jcottam": true, "jerrywu": true, "jmorris": true, "jmsmrgn": true, "jnoodle": true, "jrejaud": true, "jtinsky": true, "kairess": true, "kiinlam": true, "kkho595": true, "kontrax": true, "langjun": true, "laoshaw": true, "lesueur": true, "mamadoo": true, "mkiramu": true, "nikaoto": true, "nilz3ro": true, "perrywu": true, "pinkkis": true, "quinnjn": true, "ralucas": true, "royston": true, "rparris": true, "shivayl": true, "syntaxe": true, "thiagoh": true, "thumkus": true, "touskar": true, "tzq1011": true, "xtadmin": true, "yaramiu": true, "ziehlke": true, "akamaozu": true, "arthelon": true, "bapinney": true, "bart1208": true, "bcowgi11": true, "bmunoz89": true, "buddh!ka": true, "byoigres": true, "cslasher": true, "danday74": true, "dexteryy": true, "djamseed": true, "djviolin": true, "drazisil": true, "drveresh": true, "elrolito": true, "elussich": true, "equimper": true, "ernusame": true, "gavatron": true, "harry-sm": true, "heineiuo": true, "hugovila": true, "jlagunas": true, "jmsherry": true, "jzelaya0": true, "kogakure": true, "leejefon": true, "lekhnath": true, "leonzhao": true, "lifecube": true, "litmaj0r": true, "makediff": true, "markstos": true, "marsking": true, "mhaidarh": true, "moblogjp": true, "moimikey": true, "netojose": true, "ortonomy": true, "pablopap": true, "pddivine": true, "rizowski": true, "rkazakov": true, "ronin161": true, "rosowski": true, "schmidsi": true, "semencov": true, "slowmove": true, "ssljivic": true, "sumit270": true, "surajs21": true, "thomasfr": true, "tmurngon": true, "tofflife": true, "treatkor": true, "vchouhan": true, "voxpelli": true, "1two3code": true, "abuelwafa": true, "allendale": true, "anddoutoi": true, "andrade94": true, "antixrist": true, "artmadiar": true, "bdjunayed": true, "boneskull": true, "cascadejs": true, "chris.bit": true, "codebyren": true, "debashish": true, "dhanya-kr": true, "dylanh724": true, "elvnthaus": true, "fistynuts": true, "fleischer": true, "france193": true, "guiyuzhao": true, "heartnett": true, "hmacphail": true, "icirellik": true, "isenricho": true, "jabbalaci": true, "jacoborus": true, "jacopkane": true, "jakedalus": true, "jazzhuang": true, "jedateach": true, "joaocunha": true, "kanwisher": true, "kopepasah": true, "kswedberg": true, "largepuma": true, "larrychen": true, "letunglam": true, "lorsabyan": true, "mahamdani": true, "mallendeo": true, "max_devjs": true, "medmond78": true, "mericsson": true, "mikemimik": true, "mikroacse": true, "mjurincic": true, "nickrobes": true, "nmccready": true, "nsisodiya": true, "ramzesucr": true, "rbecheras": true, "reyronald": true, "rossdavis": true, "sdgcwoods": true, "shavidzet": true, "spences10": true, "stretchgz": true, "xiechao06": true, "yang.shao": true, "yayayahei": true, "zeroth007": true, "amaxwell01": true, "amdsouza92": true, "arjanaswal": true, "ashish.npm": true, "avivharuzi": true, "ayhabtariq": true, "bluelovers": true, "cfleschhut": true, "chancesnow": true, "clarenceho": true, "coton_chen": true, "daniellink": true, "dccunni171": true, "erictreacy": true, "farukyavuz": true, "franxyzxyz": true, "gerst20051": true, "greelgorke": true, "guyharwood": true, "hengkiardo": true, "houyishuai": true, "isaacgarza": true, "jasonevrtt": true, "jiang-xuan": true, "joelwallis": true, "justincone": true, "kuzmicheff": true, "limichange": true, "luffy84217": true, "mikejavier": true, "monkeykode": true, "monkeymonk": true, "mytharcher": true, "nelson6e65": true, "nerdtastic": true, "nerdybeast": true, "nickleefly": true, "oleg_tsyba": true, "panoptican": true, "quocnguyen": true, "ridermansb": true, "rocket0191": true, "scottmotte": true, "sean-oneal": true, "shadowlong": true, "shawnsandy": true, "shentengtu": true, "shreyawhiz": true, "stefan.age": true, "tommyldunn": true, "wenhsiaoyi": true, "windupdurb": true, "13lank.null": true, "acollins-ts": true, "ahmed_irfan": true, "bjoshuanoah": true, "brandonb927": true, "cameronjroe": true, "ciro-maciel": true, "cycomachead": true, "darlanalves": true, "digital-owl": true, "diogocapela": true, "flumpus-dev": true, "galenandrew": true, "goulash1971": true, "hongbo-miao": true, "iancrowther": true, "jacklam1988": true, "jamesbedont": true, "keithmorris": true, "kevinhassan": true, "kodekracker": true, "malloryerik": true, "mrmartineau": true, "neftedollar": true, "nisimjoseph": true, "payaamemami": true, "popey456963": true, "rci_michael": true, "reggiezhang": true, "russleyshaw": true, "sammyteahan": true, "sumitgoelpw": true, "suriyaakudo": true, "swedendrift": true, "thangakumar": true, "vparaskevas": true, "wangnan0610": true, "andrewyang96": true, "bipbipbipbip": true, "brandondoran": true, "darrentorpey": true, "dpjayasekara": true, "floriannagel": true, "grantcarthew": true, "hugojosefson": true, "ivan.marquez": true, "jabedhasan21": true, "johnny.young": true, "jorgeavaldez": true, "justinmorris": true, "lukaswilkeer": true, "marcelorisse": true, "michaelermer": true, "montanafox23": true, "mswanson1524": true, "mucahitnezir": true, "orenschwartz": true, "robert.isaev": true, "saadbinsaeed": true, "shekharreddy": true, "stevepsharpe": true, "wesleylhandy": true, "yonigoldberg": true, "yonisetiawan": true, "curioussavage": true, "dudeofawesome": true, "escapeimagery": true, "fabiomendonca": true, "gamersdelight": true, "jeremygaither": true, "jotadeveloper": true, "lassevolkmann": true, "parkerproject": true, "piyushmakhija": true, "sakthiifnotec": true, "serge-nikitin": true, "srikanth_loxa": true, "thomasmeadows": true, "tobiasalthoff": true, "armanghazaryan": true, "arnold-almeida": true, "astraloverflow": true, "danielbankhead": true, "deividasjackus": true, "joshuadavidson": true, "karlitowhoelse": true, "ms-scoped-user": true, "natarajanmca11": true, "skylor-shively": true, "suryasaripalli": true, "vandeurenglenn": true, "vipranarayan14": true, "anatolie_sernii": true, "dotnetcarpenter": true, "estrattonbailey": true, "phillippohlandt": true, "vassiliy.pimkin": true, "cristianiacobanu": true, "netoperatorwibby": true, "ys_sidson_aidson": true, "vladyslav.tserman": true, "fahadfarooqmurawat": true, "davidjsalazarmoreno": true, "fabian.moron.zirfas": true, "programmer.severson": true, "daniel-lewis-bsc-hons": true}}