
bc628a86f603eaffc2d812fbdcb304a5284bdbbe	{"key":"make-fetch-happen:request-cache:https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","integrity":"sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==","time":1753425188049,"size":1528,"metadata":{"time":1753425187842,"url":"https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","reqHeaders":{},"resHeaders":{"cache-control":"public, must-revalidate, max-age=31557600","content-type":"application/octet-stream","date":"Fri, 25 Jul 2025 06:33:07 GMT","etag":"\"f07933e3b37ddc954a8b88c9add93a0c\"","last-modified":"Tue, 30 Jun 2020 13:11:53 GMT","vary":"Accept-Encoding"},"options":{"compress":true}}}